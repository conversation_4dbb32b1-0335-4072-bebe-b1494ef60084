import { yupResolver } from "@hookform/resolvers/yup";
import {
  Box,
  Button,
  createStyles,
  Group,
  MultiSelect,
  Stack,
  Text,
} from "@mantine/core";
import { useFromFileUpload } from "@warp/client/hooks/use-form-file-upload";
import Spinner from "@warp/client/layouts/Spinner";
import {
  CreateParentCompanyMappingMutationVariables,
  FormInvitation_Insert_Input,
  FormSubmission_Insert_Input,
  InsertcompanyformfundtypeMutationVariables,
  InsertInvitationConsultantMappingMutationVariables,
  SourceFiles_Insert_Input,
  Sources_Constraint,
  WebCuration_Insert_Input,
} from "@warp/graphql/generated/types";
import { useBulkInsertFormSubmissionMutation } from "@warp/graphql/mutations/generated/bulk-insert-form-submission";
import { useBulk_Insert_SourceFilesMutation } from "@warp/graphql/mutations/generated/bulk-insert-source-files";
import { useBulkinsertwebCurationMutation } from "@warp/graphql/mutations/generated/bulk-insert-webCuration";
import { useCreateParentCompanyMappingMutation } from "@warp/graphql/mutations/generated/create-ParentCompanyMapping";
import { useInsertcompanyformfundtypeMutation } from "@warp/graphql/mutations/generated/insert-company-form-fundtype";
import { useInsertFormInvitationMutation } from "@warp/graphql/mutations/generated/insert-form-invitation";
import { useInsertIntoSubmissionTableMutation } from "@warp/graphql/mutations/generated/insert-into-submission-table";
import { useInsertInvitationConsultantMappingMutation } from "@warp/graphql/mutations/generated/insert-invitation-consultant-mapping";
import { useGetAssessorConsultantMappingQuery } from "@warp/graphql/queries/generated/get-assessor-consultant-mapping";
import { useGetBulkSuggestedDocumentsLazyQuery } from "@warp/graphql/queries/generated/get-bulk-suggested-documents-by-formid";
import { useGetCompanyByParentCompanyIdAndPlatformIdQuery } from "@warp/graphql/queries/generated/get-company-by-parent-company-id-and-platform-id";
import { useGetcompanyfundtypeavailabilityQuery } from "@warp/graphql/queries/generated/get-company-fundtype-availability";
import {
  useGetCompanyDetailByIdLazyQuery,
  useGetCompanyDetailByIdQuery,
} from "@warp/graphql/queries/generated/get-companydetail-by-id";
import { useGetCompanyDetailByparentcompanyidLazyQuery } from "@warp/graphql/queries/generated/get-companydetail-by-parentcompanyid";
import { useGetExistingFormInvitationLazyQuery } from "@warp/graphql/queries/generated/get-existing-form-invitation";
import { useGetFormInvitationByFormIdLazyQuery } from "@warp/graphql/queries/generated/get-form-invitation-by-form-id";
import { useGetformInvitationdatabycustomwhereLazyQuery } from "@warp/graphql/queries/generated/get-formInvitation-data-by-custom-where";
import { useGetglobalmasterdataforfundtypeQuery } from "@warp/graphql/queries/generated/get-globalmaster-data-for-fundtype";
import { useGetLastInvitationAnswerDetailsLazyQuery } from "@warp/graphql/queries/generated/get-last-invitation-answer-details";
import { useGetParentCompanyByCompanyAndParentCompanyIdLazyQuery } from "@warp/graphql/queries/generated/get-parentcompany-by-company-and-parentcompany-id";
import {
  AIEmailInvitation,
  AIEmailTemplates,
  AppRoles,
  FormInvitationStatus,
  invitationFormDetails,
  raraBody,
  RecommendationStatus,
  SourceFilesStatus,
  webCurationAPIDataType,
  WebDataCurationStatus,
} from "@warp/shared/constants/app.constants";
import {
  SendInvitationSelectExistingCompanySchema,
  SendInvitationSelectExistingCompanyType,
} from "@warp/shared/validation/send-invitation-select-existing-company.schema";
import dayjs from "dayjs";
import jwt from "jsonwebtoken";
import { concat } from "lodash";
import { useEffect, useMemo, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { MonthYearRangePicker } from "../../../components/MonthYearPicker";
import { useUserSession } from "../../../hooks/use-user-session";
import {
  cancelInvitationMessage,
  raraGetTokenDetails,
  sendInvitationLoadingStartedMessage,
  sendInvitationResponseMessage,
  sendInvitationValidationFailedMessage,
} from "../../../services/platform-window-message.service";
import { callAIAPI, urlToFile } from "../../form/common-functions";
import { initialState, useSendInvitationStore } from "./send-invitation/store";
const useStyles = createStyles((theme) => ({
  yearMonthPicker: {
    flexWrap: "nowrap",
  },
  actionButtons: {
    marginTop: 0,
  },
  rightSection: {
    width: "50%",
  },
}));
type parentcompanyMappingData = {
  CompanyId: string;
  parentcompanyId: string;
  ParentCompanyMappingId: string;
};
const ExistingCompanyFormFields = ({}) => {
  const postParentMessage = (message: string) =>
    window.parent?.postMessage(message, "*");
  const formInvitationWhereCondition: Record<string, any>[] = [];
  const session = useUserSession();
  const decodedToken: any =
    typeof window !== "undefined"
      ? jwt.decode(String(session?.accessToken))
      : null;
  const AITokenValue = !!decodedToken
    ? decodedToken["https://hasura.io/jwt/claims"]["x-hasura-form-with-AI"]
    : null;
  const forms_DetailBycustomWhere =
    useGetformInvitationdatabycustomwhereLazyQuery()[0];
  const { data: assessorConsultantMappingData } =
    useGetAssessorConsultantMappingQuery();
  const allConsultantIds =
    assessorConsultantMappingData?.AssessorConsultantMapping?.map(
      (d: any) => d.consultantCompanyId
    )?.filter((val: any, i: any, arr: any) => arr.indexOf(val) === i);

  const backToFormSelection = useSendInvitationStore(
    (store) => store.backToFormSelection
  );

  const selectedFormParentCompanyId = useSendInvitationStore(
    (store) => store.parentCompanyId
  );

  const cancel = useSendInvitationStore((store) => () => {
    store.init(initialState);
    postParentMessage(cancelInvitationMessage());
  });

  const { data: companyDetails } = useGetCompanyDetailByIdQuery({
    variables: {
      id: session?.company?.id,
    },
  });

  const { classes } = useStyles();

  const formParentCompanyId =
    session?.user?.role === AppRoles.Consultant
      ? selectedFormParentCompanyId
      : session?.company?.id;

  const { data: companyListData } =
    useGetCompanyByParentCompanyIdAndPlatformIdQuery({
      variables: {
        parentCompanyId: formParentCompanyId,
        platformId: session?.platform?.id,
        allConsultantCompanyIds: allConsultantIds,
      },
      fetchPolicy: "no-cache",
    });

  const formId = useSendInvitationStore((store) => store.formId);
  const formIds = useSendInvitationStore((store) => store.groupFormIds);
  const globalMasterData = useGetglobalmasterdataforfundtypeQuery();

  // const globalMasterData1 = getLocalStorageData(
  //   window.localStorage,
  //   "warp_GlobalMasterData"
  // );
  // let globalMasterData11: any = JSON.parse(globalMasterData1);
  // const { data: globalMasterData } = globalMasterData11?.GlobalMaster?.filter(
  //   (x: any) => x.type === "companyFundType"
  // );
  const vcfundtypedata = useGetcompanyfundtypeavailabilityQuery({
    variables: {
      formId: formId,
      vcCompanyId:
        session?.user?.role === AppRoles.Consultant
          ? selectedFormParentCompanyId
          : session?.company?.id,
    },
  });
  let globaldata = globalMasterData?.data?.GlobalMaster[0]?.data?.filter(
    (x: any) =>
      x.companyId == session?.company?.id &&
      x.formId?.filter((d: any) => d == formId)?.length > 0
  );
  let secondformid: any = "";
  if (globaldata?.length > 0) {
    secondformid = globaldata[0]?.formId?.filter((d: any) => d !== formId)[0];
  }
  const vcfundtypedataforsecondform = useGetcompanyfundtypeavailabilityQuery({
    variables: {
      formId: secondformid,
      vcCompanyId:
        session?.user?.role === AppRoles.Consultant
          ? selectedFormParentCompanyId
          : session?.company?.id,
    },
  });
  // const groupId = useSendInvitationStore((store) => store.groupFormId);

  const getExistingFromInvitationDetails =
    useGetExistingFormInvitationLazyQuery()[0];
  const insertWebCuration = useBulkinsertwebCurationMutation()[0];
  const lastInvitationData = useGetLastInvitationAnswerDetailsLazyQuery()[0];
  const [submitProgress, setSubmitProgress] = useState(false);

  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [clickcount, setclickcount] = useState(0);
  const [isSuccessError, setIsSuccessError] = useState(false);
  const today = new Date();
  let _fromDate = dayjs()
    .startOf("month")
    .set("month", today.getMonth() - 1)
    .toDate();
  let _toDate = dayjs()
    .endOf("month")
    .set("month", today.getMonth() - 1)
    .toDate();
  const { setError, handleSubmit, control, reset, clearErrors } =
    useForm<SendInvitationSelectExistingCompanyType>({
      resolver: yupResolver(SendInvitationSelectExistingCompanySchema),
      defaultValues: {
        duration: { fromDate: _fromDate, toDate: _toDate },
        selectedCompanyIds: [],
      },
    });

  const insertFormInvitation = useInsertFormInvitationMutation()[0];
  const insertNewSubmissionMutation = useInsertIntoSubmissionTableMutation()[0];
  const insertInvitationConsultantMapping =
    useInsertInvitationConsultantMappingMutation()[0];
  const getCompanyDetailById = useGetCompanyDetailByIdLazyQuery()[0];
  const insertCompanyFormFundType = useInsertcompanyformfundtypeMutation()[0];
  const insertParentCompanyMapping = useCreateParentCompanyMappingMutation()[0];
  const useGetCompanyDetailByparentcompanyid =
    useGetCompanyDetailByparentcompanyidLazyQuery()[0];
  const bulkInsertFormSubmission = useBulkInsertFormSubmissionMutation()[0];
  const suggestedDocsList = useGetBulkSuggestedDocumentsLazyQuery()[0];
  const { uploadFile } = useFromFileUpload();
  const [getTokendetails, setTokendetails] = useState<{
    token?: string;
    serviceURL?: string;
  }>({});
  const insertSourceFiles = useBulk_Insert_SourceFilesMutation()[0];

  const recommendationNewResponce: any = session?.GlobalMaster?.filter(
    (x: any) => x.type === "Recommendation_new"
  );
  // const { data: recommendationNewResponce } = useGetGlobalMasterByTypeQuery({
  //   variables: { type: "Recommendation_new" },
  // });
  const getFormInvitation = useGetFormInvitationByFormIdLazyQuery()[0];

  let recommendationNewData;
  let recommendationNewFormDetails: any;
  if (!!recommendationNewResponce?.length && formId) {
    recommendationNewData = recommendationNewResponce;
  }
  const CheckIndustryDetails = async (companyId: string) => {
    const { data, error } = await getCompanyDetailById({
      variables: {
        id: companyId,
      },
    });
    return data;
  };
  const GetParentCompanyExists =
    useGetParentCompanyByCompanyAndParentCompanyIdLazyQuery()[0];
  const CompanyDetailByparentcompanyid = async (
    companyId: string,
    parentCompanyId: any
  ) => {
    const { data, error } = await useGetCompanyDetailByparentcompanyid({
      variables: {
        companyId: companyId,
        parentCompanyId: parentCompanyId,
      },
    });
    return data;
  };

  const checkExistingFromInvitationDetails = async (
    checkGroupOrFormid: any,
    fromDate: Date,
    toDate: Date,
    selectedCompanyIds: any[]
  ) => {
    try {
      const getData = await getExistingFromInvitationDetails({
        variables: {
          formId: checkGroupOrFormid,
          durationFrom: fromDate,
          durationTo: toDate,
          companyId: selectedCompanyIds,
          parentcompanyId:
            session?.user?.role === AppRoles.Consultant
              ? companyDetails?.Company[0].ParentCompany?.id
              : session?.company?.id,
        },
        fetchPolicy: "no-cache",
      }).catch((e) => {
        return e;
      });
      return getData;
    } catch (e) {
      console.log(e);
      return { error: true };
    }
  };

  const data = useMemo(() => {
    let consultantDetail: any = [];

    console.log({ companyListData });
    if (session?.user?.role === AppRoles.Inviter) {
      consultantDetail =
        companyDetails?.Company[0]?.AssessorConsultantMappings.filter(
          (rec: any) => rec.assessorCompanyId === session?.company?.id
        );
    }

    // adding condition for consultant and inviter.
    if (
      formId !== null &&
      formId === "012a2471-c0a3-46ea-b6a0-19c2d2bb6de5" &&
      session?.user?.role !== AppRoles.Consultant &&
      session?.user?.role !== AppRoles.Inviter
    ) {
      return (
        companyListData?.Company?.filter(
          (company) =>
            !!company?.primaryContact?.email && company?.IsManufacturing
        ).map((company) => ({ value: company.id, label: company.name })) ?? []
      );
    } else {
      return (
        companyListData?.Company?.filter(
          (company) =>
            !!company?.primaryContact?.email &&
            (session?.user?.role === AppRoles.Consultant // removing consultant company record.
              ? company.id !== session?.company?.id
              : company.id !== consultantDetail[0]?.consultantCompanyId)
        )
          .filter(
            (company) => !!company?.id && company?.id !== session?.company?.id
          )
          .map((company) => ({ value: company.id, label: company.name })) ?? []
      );
    }
  }, [
    companyListData,
    formId,
    session?.company?.id,
    session?.user?.role,
    companyDetails,
  ]);

  const userSession = useUserSession();

  const getGroupFormids = (GroupForms: any[]) => {
    let industry: any[] = [];
    let groupFormIds: any[] = [];
    const GroupData = GroupForms.map((index: any) => {
      groupFormIds = concat(groupFormIds, index.formId);
      const industryData: any =
        index?.Form?.Details?.industry?.length > 0 &&
        index?.Form?.Details?.industry?.map((x: any) => x);
      industry = concat(industry, industryData);
    });
    return { formId: groupFormIds, industry: industry };
  };

  const checkGroupOrFormid = () => {
    let newFormId: any = [];
    newFormId = concat(newFormId, formId);
    const getFormIds = getGroupFormids(formIds);
    if (getFormIds?.formId?.length > 0) {
      const formArray = concat(getFormIds.formId, formId);
      return formArray;
    }
    return newFormId;
  };
  const checkIndustrySelected = async (companyId: any) => {
    let Final_formId = "";

    const data = await CheckIndustryDetails(companyId);

    const getFormIds = getGroupFormids(formIds);
    if (
      getFormIds?.industry?.length > 0 &&
      (data?.Company[0]?.metadata ?? "") !== "" &&
      data?.Company[0]?.metadata?.industry !== ""
    ) {
      formIds?.map((index: any) => {
        if (index?.Form?.Details?.industry?.length > 0) {
          if (
            index?.Form?.Details?.industry[0] ===
            data?.Company[0]?.metadata?.industry
          ) {
            Final_formId = index?.formId;
          }
        }
      });

      // if (data?.Company[0]?.metadata?.industry === "Agtech") {
      //   Final_formId = "c20eeb83-43a7-4bb4-9135-84c10b486ce6"; // Agtech
      // }

      // if (data?.Company[0]?.metadata?.industry === "Fintech") {
      //   Final_formId = "9d5c337b-303f-4af5-a830-75faeb5aef96"; // fintech
      // }
      return Final_formId !== "" ? Final_formId : formId;
    }

    // return groupId ?? "";
    return formId;
  };
  const checkParentCompanyExists = async (
    companyId: any,
    parentCompanyId: any
  ) => {
    const { data, error } = await GetParentCompanyExists({
      variables: {
        companyId: companyId,
        parentCompanyId: parentCompanyId,
      },
    });
    return data;
  };
  const clickHandler = (event: any) => {
    setclickcount(event.detail);
    if (event.detail == 1) {
      setclickcount(event.detail);
    }
  };

  useEffect(() => {
    if (!getTokendetails.hasOwnProperty("token")) {
      postParentMessage(raraGetTokenDetails());
    }

    globalThis.addEventListener("message", async (event: any) => {
      event.preventDefault();
      let messageData: any;
      let dataType = typeof event.data;
      if (dataType === "string") {
        try {
          messageData = JSON.parse(event.data);
          const type = messageData.type;
          if (type === "snowkap-tokendetails") {
            setTokendetails({
              token: messageData.token,
              serviceURL: messageData.serviceurl,
            });
          }
        } catch (error) {}
      }
    });
  }, [getTokendetails]);
  const replicateExistingSourceFileData = async (
    formInvitationData: webCurationAPIDataType[]
  ) => {
    //#region for copy file from other forms
    const suggestedDocs = await suggestedDocsList({
      variables: {
        formId: formInvitationData.map((items) => items?.form_id),
      },
    });
    const allFormIds: string[] = [];
    suggestedDocs.data?.SuggestedDocuments?.forEach((dItems) => {
      dItems?.metaData?.associatedFormIds
        .filter((fItems: string) => fItems != dItems?.formId)
        .forEach((items: string) => {
          if (allFormIds.filter((data) => data == items).length == 0) {
            allFormIds.push(items);
          }
        });
    });
    const LinkedFormsSuggestedDocs = await suggestedDocsList({
      variables: {
        formId: allFormIds,
      },
    });
    // where: {formId: {_in: $formId}, companyId:{_eq:$companyId}, isActive: {_eq: true}, Sources: {SourceFile: {suggestedDocumentId: {_eq: $sdid}}}}, order_by: {created_at: asc}
    const otherformIds: Record<string, String>[] = [];
    const fetchFormInvitationByWhereCondition: Record<string, any>[] = [];
    LinkedFormsSuggestedDocs?.data?.SuggestedDocuments?.filter(
      (dataItems) => dataItems?.isOther == false
    )?.forEach((dItems) => {
      formInvitationData.forEach((cItems) => {
        if (
          otherformIds.filter(
            (fItems) =>
              fItems?.formId == dItems?.formId &&
              fItems?.companyId == cItems?.company_id &&
              fItems?.suggestedDocId == dItems?.id
          ).length == 0
        ) {
          otherformIds.push({
            formId: dItems?.formId,
            companyId: cItems?.company_id,
            suggestedDocId: dItems?.id,
          });
          fetchFormInvitationByWhereCondition.push({
            _and: {
              formId: { _eq: dItems?.formId },
              companyId: { _eq: cItems?.company_id },
              isActive: { _eq: true },
              Sources: {
                SourceFile: {
                  suggestedDocumentId: { _eq: dItems?.id },
                },
              },
            },
          });
        }
      });
    });
    const invitationDataDetail = await forms_DetailBycustomWhere({
      variables: {
        where: { _or: fetchFormInvitationByWhereCondition },
      },
    });
    const source_file_Insert_Input: SourceFiles_Insert_Input[] = [];
    const invitationDetailforSourceFile =
      invitationDataDetail?.data?.FormInvitation?.filter(
        (invitationItems) => invitationItems.Sources.length > 0
      );
    const raraAPIBody: raraBody[] = [];
    const pdfFileRara: raraBody[] = [];
    if (!!invitationDetailforSourceFile) {
      for (let i = 0; i < invitationDetailforSourceFile?.length; i++) {
        const fileDetails = await urlToFile(
          String(
            invitationDetailforSourceFile[i].Sources[0].SourceFile
              ?.originalFileUrl
          ),
          String(
            invitationDetailforSourceFile[i].Sources[0].SourceFile
              ?.originalFileName
          )
        );
        const fileresult = await uploadFile(
          fileDetails,
          false,
          "AI_SOURCES/" + String(invitationDetailforSourceFile[i].companyId)
        );
        //#region Creating Array for Bulk Rara Validation in background
        if (fileresult?.type == "pdf") {
          pdfFileRara.push({
            document_url: String(fileresult?.path),
            document_name: String(
              invitationDetailforSourceFile[i]?.Sources[0].SourceFile
                ?.SuggestedDocument?.title
            ),
            company_name: String(
              invitationDetailforSourceFile[i]?.Company?.name
            ),
            sourceFileId: "",
            parentCompanies: [],
            error: {
              error: "",
              warning: "",
            },
          });
        }
        //#endregion
        const uploadedFilePath =
          "/AI_SOURCES/" +
          String(session?.company?.id) +
          fileresult?.path.split(
            "AI_SOURCES/" + String(invitationDetailforSourceFile[i]?.companyId)
          )[
            fileresult?.path.split(
              "AI_SOURCES/" +
                String(invitationDetailforSourceFile[i]?.companyId)
            ).length - 1
          ];
        source_file_Insert_Input.push({
          originalFileName: fileresult?.name,
          originalFileUrl: fileresult?.path,
          fileName:
            fileresult?.path.split("/")[fileresult?.path.split("/").length - 1],
          filePath: uploadedFilePath,
          suggestedDocumentId:
            invitationDetailforSourceFile[i]?.Sources[0]?.SourceFile
              ?.suggestedDocumentId,
          uploadedByUserId: session?.user?.id,
          fileSize: String(fileDetails.size),
          error: {
            warning:
              invitationDetailforSourceFile[i]?.Sources[0]?.SourceFile?.error
                ?.warning,
            error: "",
          },
          status: SourceFilesStatus.Uploaded,
          Sources: {
            on_conflict: { constraint: Sources_Constraint.SourcesPkey },
            data: [
              {
                formInvitationId: invitationDetailforSourceFile[i]?.id,
                type: invitationDetailforSourceFile[i]?.Sources[0]?.type,
                url: uploadedFilePath,
                copiedFromSourceId:
                  invitationDetailforSourceFile[i]?.Sources[0]?.id,
              },
            ],
          },
        });
      }
    }
    const insertedData = await insertSourceFiles({
      variables: {
        data: source_file_Insert_Input,
      },
    });
    insertedData?.data?.insert_SourceFiles?.returning
      ?.filter((items) =>
        pdfFileRara.find(
          (dataITems) => dataITems.document_url == items?.originalFileUrl
        )
      )
      .forEach((items) => {
        raraAPIBody.push({
          document_url: String(items?.originalFileUrl),
          document_name: String(items?.originalFileName),
          company_name: String(
            items?.Sources[0]?.FormInvitation?.Company?.name
          ),
          sourceFileId: items?.id,
          parentCompanies: [],
          error: items?.error,
        });
      });
    //#region Bulk Rara Request
    fetch("/api/AI/AI-rara-document-validation", {
      method: "POST",
      body: JSON.stringify({
        raraAPIBody: raraAPIBody,
        reqUrl: getTokendetails.serviceURL + "warp/document-validation",
        configData: {
          headers: {
            Authorization: "Bearer " + getTokendetails.token,
            "Content-Type": "application/json",
          },
        },
        isDBUpdation: true,
      }),
      headers: {
        "Content-Type": "application/json",
      },
    });
    //#endregion
    //#endregion
  };
  const onSubmit = async (data: SendInvitationSelectExistingCompanyType) => {
    let formDetails: invitationFormDetails[] = [];
    setSubmitProgress(true);
    let isError = false;
    let _success = false;
    if (clickcount === 1) {
      try {
        // Check duration range
        if (data.duration.fromDate > data.duration.toDate) {
          if (!isError) {
            setSubmitProgress(false);
            isError = true;
            setError("duration", {
              message:
                "Assessment period selected is invalid. Please select valid period.",
            });
          }
          return;
        }

        //if (!!recommendationNewFormDetails?.length) {
        // const newDate = new Date();
        // const previousMonthYear =
        //   newDate.getFullYear() + "-" + (newDate.getMonth() + 1);

        let currentMonthYear = dayjs().startOf("month").toDate();

        currentMonthYear.setHours(currentMonthYear.getHours() + 5);
        currentMonthYear.setMinutes(currentMonthYear.getMinutes() + 30);

        // const fromMonthYear =
        //   data.duration.fromDate.getFullYear() +
        //   "-" +
        //   (data.duration.fromDate.getMonth() + 1);
        // const toMonthYear =
        //   data.duration.toDate.getFullYear() +
        //   "-" +
        //   (data.duration.toDate.getMonth() + 1);

        // const fromDate = new Date(fromMonthYear);
        // const toDate = new Date(toMonthYear);
        // const previousDate = new Date(previousMonthYear);

        // let date = new Date();
        let finalFromDate = data.duration.fromDate;
        //  new Date(
        //   date.getFullYear(),
        //   data.duration.fromDate.getMonth(),
        //   1
        // );

        let finalToDate = data.duration.toDate;
        // new Date(
        //   date.getFullYear(),
        //   data.duration.toDate.getMonth() + 1,
        //   0
        // ); //last month

        finalFromDate.setHours(finalFromDate.getHours() + 5);
        finalFromDate.setMinutes(finalFromDate.getMinutes() + 30);

        finalToDate.setHours(finalToDate.getHours() + 5);
        finalToDate.setMinutes(finalToDate.getMinutes() + 30);

        if (
          currentMonthYear >= finalFromDate &&
          currentMonthYear >= finalToDate
        ) {
          const response = await getFormInvitation({
            variables: {
              formId: formId,
              companyId: data?.selectedCompanyIds,
            },
            fetchPolicy: "no-cache",
          })
            .then((rec) => {
              return rec.data;
            })
            .catch((error) => {
              console.log({ error });
            });

          if (!!response?.FormInvitation?.length) {
            let unSubmittedForm: any = [];
            data?.selectedCompanyIds?.map((cId) => {
              response?.FormInvitation?.filter((rec) => {
                if (rec.formId === formId && rec.companyId === cId) {
                  if (rec.status !== "Approved") {
                    unSubmittedForm.push(rec);
                  }
                }
              });
            });

            if (!!unSubmittedForm.length) {
              if (!isError) {
                setSubmitProgress(false);
                isError = true;
                setError("duration", {
                  message:
                    "Past assessment is not submitted yet for this questionnaire.",
                });
              }
              return;
            }
          }
        } else {
          if (!isError) {
            isError = true;
            setSubmitProgress(false);
            setError("duration", {
              message:
                "Please select the historical months only for assessment period.",
            });
          }
          return;
        }
        //}

        // Validating existing data
        const result = await checkExistingFromInvitationDetails(
          checkGroupOrFormid(),
          finalFromDate,
          finalToDate,
          data.selectedCompanyIds
        );
        if (result?.error) {
          if (!isError) {
            isError = true;
            setSubmitProgress(false);
            setErrorMessage("Assessment is already taken for selected period");
          }
          return;
        }
        if (!!result?.data && result?.data?.FormInvitation?.length > 0) {
          if (!isError) {
            isError = true;
            setSubmitProgress(false);
            setErrorMessage("Assessment is already taken for selected period");
          }
          return;
        }
        let parentcompanymapping: CreateParentCompanyMappingMutationVariables =
          {
            input: [],
          };
        let forminvitationfundTypeArray: InsertcompanyformfundtypeMutationVariables =
          {
            object: [],
          };
        let invitationConsultantMappingDataArray: InsertInvitationConsultantMappingMutationVariables =
          {
            object: [],
          };
        if (isError === false) {
          setSubmitProgress(true);
          setErrorMessage(null);
          setIsSuccessError(false);
          const checkedData: parentcompanyMappingData[] = [];

          for (let g = 0; g < data.selectedCompanyIds.length; g++) {
            const getparentcompanydata = await checkParentCompanyExists(
              data.selectedCompanyIds[g],
              session?.company?.id
            );
            if (
              !!getparentcompanydata &&
              getparentcompanydata?.ParentCompanyMapping?.length > 0
            ) {
              getparentcompanydata?.ParentCompanyMapping.forEach((mapping) => {
                checkedData.push({
                  CompanyId: data.selectedCompanyIds[g],
                  parentcompanyId:
                    session?.user?.role === AppRoles.Consultant
                      ? companyDetails?.Company[0].ParentCompany?.id
                      : session?.company?.id,
                  ParentCompanyMappingId: mapping?.Id,
                });
              });
            } else {
              checkedData.push({
                CompanyId: data.selectedCompanyIds[g],
                parentcompanyId:
                  session?.user?.role === AppRoles.Consultant
                    ? companyDetails?.Company[0].ParentCompany?.id
                    : session?.company?.id,
                ParentCompanyMappingId: "",
              });
            }
          }
          const newParentCompany = checkedData.filter(
            (x: any) => x.ParentCompanyMappingId === ""
          );
          const existingParentCompany = checkedData.filter(
            (x: any) => x.ParentCompanyMappingId !== ""
          );
          let formInvitationDataArray: FormInvitation_Insert_Input[] = [];
          if (!!existingParentCompany.length) {
            for (let g = 0; g < existingParentCompany.length; g++) {
              const insertFormId = await checkIndustrySelected(
                existingParentCompany[g]?.CompanyId
              );
              formInvitationWhereCondition.push({
                _and: {
                  companyId: { _eq: existingParentCompany[g].CompanyId },
                  formId: { _eq: insertFormId },
                },
              });
            }
            let forms_FormInvitationDetail: any = [];
            if (formInvitationWhereCondition.length > 0) {
              forms_FormInvitationDetail = await forms_DetailBycustomWhere({
                variables: {
                  where: { _or: formInvitationWhereCondition },
                },
              });
            }
            for (let j = 0; j < existingParentCompany.length; j++) {
              const _company = companyListData?.Company?.find(
                (m) => m.id === existingParentCompany[j].CompanyId
              );
              if (!_company) throw setErrorMessage("Invalid company found");
              const GetformId = await checkIndustrySelected(
                existingParentCompany[j].CompanyId
              );
              const formAIData =
                !!AITokenValue &&
                AITokenValue?.filter((items: any) => items?.formId == GetformId)
                  .length > 0
                  ? AITokenValue?.filter(
                      (items: any) =>
                        items?.formId == GetformId && items?.docWithAI == true
                    )
                  : [];
              formInvitationDataArray.push({
                companyId: existingParentCompany[j].CompanyId,
                formId: GetformId,
                email: _company?.primaryContact?.email,
                status:
                  forms_FormInvitationDetail?.data?.FormInvitation?.filter(
                    (items: Record<string, any>) =>
                      items.companyId == existingParentCompany[j].CompanyId &&
                      items.formId == GetformId
                  ).length == 0
                    ? formAIData.length > 0
                      ? FormInvitationStatus.Processing
                      : FormInvitationStatus.Invited
                    : FormInvitationStatus.Invited,
                created_by: session?.user?.id,
                updated_by: session?.user?.id,
                durationFrom: finalFromDate,
                durationTo: finalToDate,
                parentcompanyId:
                  session?.user?.role === AppRoles.Consultant
                    ? companyDetails?.Company[0].ParentCompany?.id
                    : session?.company?.id,
                ParentCompanyMappingId:
                  existingParentCompany[j].ParentCompanyMappingId,
              });
            }
          }
          if (newParentCompany?.length > 0) {
            parentcompanymapping.input = newParentCompany.map((rec: any) => {
              const data1 = {
                CompanyId: rec.CompanyId,
                ParentCompanyId:
                  session?.user?.role === AppRoles.Consultant
                    ? selectedFormParentCompanyId
                    : rec.parentcompanyId,
              };
              return data1;
            });
            const result1 = await insertParentCompanyMapping({
              variables: parentcompanymapping,
            });
            if (
              result1 &&
              result1?.data?.insert_ParentCompanyMapping?.returning
            ) {
              for (
                let h = 0;
                h <
                result1?.data?.insert_ParentCompanyMapping?.returning.length;
                h++
              ) {
                const insertFormId = await checkIndustrySelected(
                  result1?.data?.insert_ParentCompanyMapping?.returning[h]
                    ?.CompanyId
                );
                formInvitationWhereCondition.push({
                  _and: {
                    companyId: {
                      _eq: result1?.data?.insert_ParentCompanyMapping
                        ?.returning[h].CompanyId,
                    },
                    formId: { _eq: insertFormId },
                  },
                });
              }
              let forms_FormInvitationDetail: any = [];
              if (formInvitationWhereCondition.length > 0) {
                forms_FormInvitationDetail = await forms_DetailBycustomWhere({
                  variables: {
                    where: { _or: formInvitationWhereCondition },
                  },
                });
              }
              for (
                let invitationData = 0;
                invitationData <
                result1?.data?.insert_ParentCompanyMapping?.returning.length;
                invitationData++
              ) {
                const _company = companyListData?.Company?.find(
                  (m) =>
                    m.id ===
                    result1?.data?.insert_ParentCompanyMapping?.returning[
                      invitationData
                    ].CompanyId
                );
                if (!_company) throw setErrorMessage("Invalid company found");
                const GetformId = await checkIndustrySelected(
                  result1?.data?.insert_ParentCompanyMapping?.returning[
                    invitationData
                  ].CompanyId
                );
                const formAIData =
                  !!AITokenValue &&
                  AITokenValue?.filter(
                    (items: any) => items?.formId == GetformId
                  ).length > 0
                    ? AITokenValue?.filter(
                        (items: any) =>
                          items?.formId == GetformId && items?.docWithAI == true
                      )
                    : [];
                formInvitationDataArray.push({
                  companyId:
                    result1?.data?.insert_ParentCompanyMapping?.returning[
                      invitationData
                    ].CompanyId,
                  formId: GetformId,
                  email: _company?.primaryContact?.email,
                  status:
                    forms_FormInvitationDetail?.data?.FormInvitation?.filter(
                      (items: Record<string, any>) =>
                        items.companyId ==
                          result1?.data?.insert_ParentCompanyMapping?.returning[
                            invitationData
                          ].CompanyId && items.formId == GetformId
                    ).length == 0
                      ? formAIData.length > 0
                        ? FormInvitationStatus.Processing
                        : FormInvitationStatus.Invited
                      : FormInvitationStatus.Invited,
                  created_by: session?.user?.id,
                  updated_by: session?.user?.id,
                  durationFrom: finalFromDate,
                  durationTo: finalToDate,
                  parentcompanyId:
                    session?.user?.role === AppRoles.Consultant
                      ? companyDetails?.Company[0].ParentCompany?.id
                      : session?.company?.id,
                  ParentCompanyMappingId:
                    result1?.data?.insert_ParentCompanyMapping?.returning[
                      invitationData
                    ].Id,
                });
              }
            }
            // const parentCompanyId = session?.company?.id;
            // const userId = session?.user?.id;

            // parentcompanymapping.input = {
            //   CompanyId: companyIds[0],
            //   ParentCompanyId: session?.company?.id,
            // };

            // const getparentcompanydata = await CompanyDetailByparentcompanyid(
            //   companyIds[0],
            //   parentCompanyId
            // );
            // if (getparentcompanydata?.Company.length === 0) {
            //   const result1 = await insertParentCompanyMapping({
            //     variables: parentcompanymapping,
            //   });
            // }
          }
          postParentMessage(sendInvitationLoadingStartedMessage());
          const companyIds = formInvitationDataArray.map(
            (item: FormInvitation_Insert_Input) => {
              return item.companyId;
            }
          );
          recommendationNewFormDetails =
            recommendationNewResponce[0]?.data?.filter(
              (rec: any) => rec.FormId === formInvitationDataArray[0]?.formId
            );
          let lastInvitationDataQuery: any = [];
          if (recommendationNewFormDetails?.length > 0) {
            lastInvitationDataQuery = await lastInvitationData({
              variables: {
                companyId: companyIds,
                formId: formInvitationDataArray[0].formId,
                status: [RecommendationStatus.Closed, RecommendationStatus.NA],
                parentcompanyId: session?.company?.id,
              },
              fetchPolicy: "no-cache",
            });
          }
          const { data: insertFormInvitationResult } =
            await insertFormInvitation({
              variables: {
                object: formInvitationDataArray,
                companyId: companyIds[0],
                // parentCompanyId: parentCompanyId,
                //userId: userId,
              },
            });
          // Insert record to InvitationConsultantMapping Table.
          if (
            insertFormInvitationResult &&
            insertFormInvitationResult?.insert_FormInvitation?.returning
          ) {
            if (
              recommendationNewFormDetails?.length > 0 &&
              lastInvitationDataQuery?.data?.LastFormInvitation[0]
                ?.FormInvitations?.length > 0
            ) {
              let bodyobject = {
                iscarryforwardImplemented: recommendationNewFormDetails,
                insertFormInvitationResult:
                  insertFormInvitationResult?.insert_FormInvitation?.returning,
                lastInvitationDataQuery: lastInvitationDataQuery?.data,
              };
              await fetch("/api/carry-forward-assessment-data", {
                method: "POST",
                body: JSON.stringify(bodyobject),
                headers: {
                  "Content-Type": "application/json",
                  Authorization: userSession?.accessToken ?? "",
                },
              });
            }
          }
          if (session?.user?.role === AppRoles.Consultant) {
            if (
              insertFormInvitationResult &&
              insertFormInvitationResult?.insert_FormInvitation?.returning
            ) {
              const data =
                insertFormInvitationResult?.insert_FormInvitation?.returning;

              data.map(async (rec) => {
                invitationConsultantMappingDataArray.object = {
                  invitationId: rec?.id,
                  consultantUserId: session?.user?.id,
                  consultantCompanyId: session?.company?.id,
                };

                const resultCunsultantMapping =
                  await insertInvitationConsultantMapping({
                    variables: invitationConsultantMappingDataArray,
                  });
              });
            }
          }

          if (globaldata[0]?.IsEnable) {
            let insertfundtypearray: any = [];
            data.selectedCompanyIds.map(async (companyId: any) => {
              if (
                vcfundtypedata.data?.CompanyFormFundtype?.filter(
                  (x: any) => x.companyId === companyId
                )?.length === 0
              ) {
                // const GetformId = checkIndustrySelected(companyId);
                insertfundtypearray.push({
                  vcCompanyId:
                    session?.user?.role === AppRoles.Consultant
                      ? selectedFormParentCompanyId
                      : session?.company?.id,
                  companyId: companyId,
                  formId: formId,
                  invitedBy: session?.user?.id,
                  fundType: {
                    fundType:
                      vcfundtypedataforsecondform?.data?.CompanyFormFundtype?.filter(
                        (x: any) => x.companyId === companyId
                      )[0]?.fundType?.fundType,
                  },
                });
              }
            });
            forminvitationfundTypeArray.object = insertfundtypearray;
            const fundtypeinsertion = await insertCompanyFormFundType({
              variables: forminvitationfundTypeArray,
            });
          }
          const success =
            insertFormInvitationResult?.insert_FormInvitation?.returning &&
            insertFormInvitationResult?.insert_FormInvitation?.returning
              ?.length > 0;
          if (!success) {
            setErrorMessage("Failed to send invitation");
            postParentMessage(sendInvitationValidationFailedMessage());
          } else {
            formDetails =
              insertFormInvitationResult?.insert_FormInvitation?.returning.map(
                (inviteItems) => {
                  const companyData =
                    lastInvitationDataQuery?.data?.LastFormInvitation.filter(
                      (items: any) => items.id == inviteItems?.companyId
                    );
                  let inviteData = [];
                  if (!!companyData && companyData.length > 0) {
                    inviteData = companyData[0]?.FormInvitations?.filter(
                      (items: any) => items?.id == inviteItems?.id
                    );
                  }
                  const formData =
                    recommendationNewFormDetails?.length > 0 &&
                    inviteData?.length > 0
                      ? []
                      : !!AITokenValue && AITokenValue.length > 0
                      ? AITokenValue?.filter(
                          (items: any) =>
                            items?.formId == inviteItems?.formId &&
                            (items?.docWithAI == true || items?.onlyDoc == true)
                        )
                      : [];
                  return {
                    isAIForm: formData.length > 0,
                    AIData: formData,
                  };
                }
              ) as invitationFormDetails[];
            const processingInvitation =
              insertFormInvitationResult?.insert_FormInvitation?.returning.filter(
                (items) => items.status == FormInvitationStatus.Processing
              );
            const invitedStatusInvitation =
              insertFormInvitationResult?.insert_FormInvitation?.returning
                .filter((items) => items.status == FormInvitationStatus.Invited)
                .map((items) => {
                  const companyData =
                    lastInvitationDataQuery?.data?.LastFormInvitation.filter(
                      (inviteItems: any) => inviteItems.id == items?.companyId
                    );
                  let inviteData = [];
                  if (!!companyData && companyData.length > 0) {
                    inviteData = companyData[0]?.FormInvitations?.filter(
                      (inviteItems: any) => inviteItems?.id == items?.id
                    );
                  }
                  return {
                    invitationId: items.id,
                    isNormalInvitation:
                      recommendationNewFormDetails?.length > 0 &&
                      inviteData?.length > 0
                        ? true
                        : !!AITokenValue && AITokenValue.length > 0
                        ? AITokenValue.filter(
                            (datItems: any) =>
                              datItems?.formId == items.formId &&
                              datItems?.onlyDoc == true
                          ).length == 0
                        : true,
                    companyId: items?.companyId,
                    formId: items?.formId,
                    platformId: session?.platform?.id,
                  };
                });
            if (!!processingInvitation && processingInvitation.length > 0) {
              const bulkInsertionData: FormSubmission_Insert_Input[] = [];
              const webCurationInsertInput: WebCuration_Insert_Input[] = [];
              processingInvitation?.forEach((items) => {
                bulkInsertionData.push({
                  invitationId: items?.id,
                  isActive: true,
                });
                webCurationInsertInput.push({
                  formInvitationId: items?.id,
                  status: WebDataCurationStatus.Processing,
                  triggeredByUserId: session?.user?.id,
                  startAt: new Date(),
                });
              });
              await bulkInsertFormSubmission({
                variables: {
                  formSubmissionInput: bulkInsertionData,
                },
              });
              const webCurationInsertion = await insertWebCuration({
                variables: {
                  webCurationInsertInput: webCurationInsertInput,
                },
              });
              const formInvitationData = processingInvitation?.map((items) => {
                return {
                  company_id: items?.companyId,
                  form_id: items?.formId,
                  form_invitation_id: items?.id,
                  web_curation_id:
                    !!webCurationInsertion?.data?.insert_WebCuration
                      ?.returning &&
                    webCurationInsertion?.data?.insert_WebCuration?.returning
                      .length > 0
                      ? webCurationInsertion?.data?.insert_WebCuration?.returning.filter(
                          (curationItems) =>
                            curationItems?.formInvitationId == items?.id
                        )[0]?.id
                      : "",
                };
              });
              if (!!formInvitationData && formInvitationData.length > 0) {
                await callAIAPI(formInvitationData);
                await replicateExistingSourceFileData(formInvitationData);
              }
            }
            if (
              !!invitedStatusInvitation &&
              invitedStatusInvitation.length > 0
            ) {
              if (
                invitedStatusInvitation.filter(
                  (items) => !items.isNormalInvitation
                ).length > 0
              ) {
                const bulkInsertionData: FormSubmission_Insert_Input[] = [];
                const emailInviteData = invitedStatusInvitation
                  .filter((items) => !items.isNormalInvitation)
                  .map((invitedItems) => {
                    bulkInsertionData.push({
                      invitationId: invitedItems?.invitationId,
                      isActive: true,
                    });
                    return {
                      invitationId: String(invitedItems?.invitationId),
                      isInternalUSer: false,
                      emailType: [
                        AIEmailTemplates.AINewFormInvitation,
                        AIEmailTemplates.AINewOnboarding,
                      ],
                    };
                  }) as AIEmailInvitation[];
                await bulkInsertFormSubmission({
                  variables: {
                    formSubmissionInput: bulkInsertionData,
                  },
                });
                if (emailInviteData.length > 0) {
                  await fetch("/api/AI/email-invitation", {
                    method: "POST",
                    headers: {
                      "content-type": "application/json",
                    },
                    body: JSON.stringify(emailInviteData),
                  });
                }
              }
              // send data to API for company email invitation.

              const sendInvitationEmailPromisses =
                invitedStatusInvitation
                  .filter((items) => items.isNormalInvitation)
                  .map(async (items) => {
                    await fetch("/api/email-invitation", {
                      method: "POST",
                      headers: {
                        "content-type": "application/json",
                      },
                      body: JSON.stringify({
                        id: items?.invitationId,
                        type: "ExistingCompanyFormInvitation",
                        companyId: items?.companyId,
                        formId: formId,
                        platformId: session?.platform?.id,
                      }),
                    });
                  }) ?? [];

              await Promise.all(sendInvitationEmailPromisses);
            }
            setIsSuccessError(true);
            cancel();
            reset();
            _success = true;
          }
        }
      } catch (error) {
        setSubmitProgress(false);
        console.log("Send Invitation Submit Error", { error });
        setErrorMessage("Something went wrong");
        postParentMessage(sendInvitationValidationFailedMessage());
      }

      if (_success === true) {
        postParentMessage(sendInvitationResponseMessage(_success, formDetails));
        setSubmitProgress(false);
      }
    } else {
      setSubmitProgress(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} noValidate>
      {/* <LoadingOverlay visible={submitProgress} /> */}
      <Spinner visible={submitProgress} />
      <Stack mt={10} spacing="md">
        <Text size={12} color="dark.3">
          Select one or multiple partners (registered on Snowkap) from the list
        </Text>
        <Controller
          control={control}
          name="selectedCompanyIds"
          defaultValue={[]}
          render={({
            field: { name, onBlur, onChange, ref, value },
            fieldState: { error },
          }) => {
            return (
              <Stack spacing="xs" style={{ gap: "3px" }}>
                <MultiSelect
                  data={data.sort((a, b) => a.label.localeCompare(b.label))}
                  placeholder="Click to search or select partners"
                  searchable
                  clearable
                  onChange={(val) => {
                    onChange(val);
                    clearErrors("duration");
                    setErrorMessage(null);
                  }}
                  onBlur={onBlur}
                  name={name}
                  value={value}
                  ref={ref}
                />
                {error && (
                  <Text size={12} color="radioCheckBoxError.0">
                    {error.message}
                  </Text>
                )}
              </Stack>
            );
          }}
        />
        <Text size={12} style={{ color: "#212529", fontWeight: "500" }}>
          Select Period
        </Text>
        <Text size={12} color="dark.3">
          Select the historical period, month-to-month, of which the data is
          sought
        </Text>
        <Text size={12} color="dark.3">
          No partial months can be selected
        </Text>

        <Controller
          control={control}
          name="duration"
          render={({ field, fieldState: { error } }) => {
            return (
              <Stack spacing="xs">
                <Group
                  sx={{ flexWrap: "nowrap" }}
                  spacing={160}
                  // spacing="xs"
                  position="left"
                >
                  <Text
                    size={12}
                    style={{ color: "#212529", fontWeight: "500" }}
                  >
                    From
                  </Text>
                  <Text
                    size={12}
                    style={{ color: "#212529", fontWeight: "500" }}
                  >
                    To
                  </Text>
                </Group>

                <MonthYearRangePicker
                  onChange={field.onChange}
                  value={field.value}
                />
                {error && (
                  <Text size={12} color="radioCheckBoxError.0">
                    {error.message}
                  </Text>
                )}
              </Stack>
            );
          }}
        />

        {errorMessage && (
          <Text size={12} color="radioCheckBoxError.0">
            {errorMessage}
          </Text>
        )}
        {/* {isSuccessError && (
          <Text size={12} color="green">
            Assessment invitation send successfully.
          </Text>
        )} */}
        <Group className={classes.actionButtons} position="right" spacing="sm">
          <Button color="outlineBtn" onClick={cancel}>
            Cancel
          </Button>
          <Button color="solidBtn" onClick={backToFormSelection}>
            Previous
          </Button>
          <Button
            type="submit"
            color="solidBtn"
            disabled={submitProgress}
            loading={submitProgress}
            onClick={clickHandler}
          >
            Send
          </Button>
        </Group>
      </Stack>
    </form>
  );
};
const ExistingCompanyForm = () => {
  return (
    <Box>
      <ExistingCompanyFormFields />
    </Box>
  );
};
export default ExistingCompanyForm;
