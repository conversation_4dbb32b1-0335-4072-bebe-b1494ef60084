query getProcessingDataByInvitationId($invitationId: uuid!) {
  AIBulkDocumentProcessing(
    where: { formInvitationId: { _eq: $invitationId } }
    order_by: { created_at: desc }
    limit: 1
  ) {
    processedDocuments
    requestStatus
  }
  FormInvitation(where: { id: { _eq: $invitationId } }) {
    Form {
      title
    }
    Company {
      name
    }
    isDataCurationSkipped
    status
  }
}
