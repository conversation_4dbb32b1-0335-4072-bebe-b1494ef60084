require("dotenv").config({ path: "../../apps/web/.env.local" });
import { getGraphqlApiUrl } from "@warp/configs/api.config";

/** @type {import("@graphql-codegen/plugin-helpers").Types.Config} */
const codegenConfig = {
  overwrite: true,
  schema: [
    {
      [getGraphqlApiUrl()]: {
        headers: {
          "x-hasura-admin-secret": process.env["HASURA_GRAPHQL_ADMIN_SECRET"],
        },
      },
    },
  ],
  documents: [
    "./fragments/*.{gql,graphql}",
    "./queries/*.{gql,graphql}",
    "./mutations/*.{gql,graphql}",
    "./subscriptions/*.{gql,graphql}",
  ],
  generates: {
    "./generated/types.ts": {
      plugins: ["typescript", "typescript-operations"],
    },
    "./generated/apollo-helpers.ts": {
      plugins: ["typescript-apollo-client-helpers"],
    },
    "./generated/sdk.ts": {
      preset: "import-types",
      presetConfig: {
        typesPath: "./types",
        importTypesNamespace: "types",
      },
      plugins: ["typescript-graphql-request"],
      config: {
        importOperationTypesFrom: "types",
      },
    },
    "./generated": {
      preset: "near-operation-file",
      presetConfig: {
        extension: ".ts",
        baseTypesPath: "types.ts",
        folder: "generated",
      },
      plugins: ["typescript-react-apollo"],
      config: {
        importOperationTypesFrom: "Types",
      },
    },
  },
};

module.exports = codegenConfig;
