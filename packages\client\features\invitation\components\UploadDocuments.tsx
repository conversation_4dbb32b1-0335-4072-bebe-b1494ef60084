import {
  Box,
  Button,
  Center,
  Container,
  SimpleGrid,
  Space,
} from "@mantine/core";
import {
  formatFileName,
  getWebCurationData,
  isURLValid,
  urlToFile,
} from "@warp/client/features/form/common-functions";
import { useUserSession } from "@warp/client/hooks/use-user-session";
import Spinner from "@warp/client/layouts/Spinner";
import {
  calculatePercentage,
  deleteFileModal,
  hideprocessButton,
  ProcessSkipAndContinue,
  raraGetTokenDetails,
} from "@warp/client/services/platform-window-message.service";
import {
  GetSourceFilesByInvitationIdQuery,
  SourceFiles_Insert_Input,
  SourceFiles_Updates,
  Sources_Constraint,
  Sources_Updates,
} from "@warp/graphql/generated/types";
import { useBulkDeleteSourceFilesFromIdMutation } from "@warp/graphql/mutations/generated/bulk-delete-sourcefiles-from-Id";
import { useBulk_Insert_SourceFilesMutation } from "@warp/graphql/mutations/generated/bulk-insert-source-files";
import { useBulkUpdateSourceAndSourcesMutation } from "@warp/graphql/mutations/generated/bulk-update-sourcefiles-and-sources";
import { useGetCompanyDetailByIdQuery } from "@warp/graphql/queries/generated/get-companydetail-by-id";
import {
  useGetSuggestedDocumentsLazyQuery,
  useGetSuggestedDocumentsQuery,
} from "@warp/graphql/queries/generated/get-suggested-documents-by-formid";
import {
  AIDataCardsType,
  commonValues,
  DropzoneStatus,
  IngestData,
  IngestFile,
  invitationCompletion,
  rejectedFiles,
  SourceFile,
  SourceFilesStatus,
  SourcesType,
  UploadStatus,
  userInvitationAIStatus,
} from "@warp/shared/constants/app.constants";
import InfoItem from "@warp/web/pages/embed/AIBasedSections/Common/InfoItem";
import axios from "axios";
import dynamic from "next/dynamic";
import { FC, useEffect, useState } from "react";
import { useFileUploadWithProgress } from "../../../hooks/use-file-upload-with-progress";

const postParentMessage = (message: string) =>
  window.parent?.postMessage(message, "*");

interface SuggestedDocument {
  id: string;
  maxSize: number;
  sampleFileUrl: string;
  seqIndex: number;
  title: string;
  acceptedFormats: { acceptedFormats: string[] };
  isOther: boolean;
  dataPoints: number;
  warning: string;
  FileProcessingErrorMessage: string;
  sourceFileUrl: string;
  copiedFromSourceId?: string;
}
interface UploadFileStatData {
  file: File;
  metadata: string;
  fileUrl: string;
  sourceFileId: string;
  warning: string;
  FileProcessingErrorMessage: string;
}

const UploadDocuments: FC<{
  formId: string;
  submissionId: string;
  invitationId: string;
  pageLoadSourceFileData: GetSourceFilesByInvitationIdQuery;
  dataInFirstRow: number;
  AIData: userInvitationAIStatus[];
  AIFormCardData: AIDataCardsType;
}> = ({
  formId,
  submissionId,
  invitationId,
  pageLoadSourceFileData,
  dataInFirstRow,
  AIData,
  AIFormCardData,
}) => {
  const invitationSourceItem = pageLoadSourceFileData?.Sources?.filter(
    (dataItems) => dataItems?.type == SourcesType.Uploaded.dbTittle
  );
  const invitationUploadedDocument = pageLoadSourceFileData?.Sources?.filter(
    (dataItems) =>
      dataItems?.type == SourcesType.Uploaded.dbTittle &&
      dataItems?.SourceFile?.status == SourceFilesStatus.Uploaded
  );
  const { uploadFile } = useFileUploadWithProgress();
  const userSession = useUserSession();
  const customDropzoneIdPrefix = "custom-";
  const insertSourceFiles = useBulk_Insert_SourceFilesMutation()[0];
  const updateSourceFiles = useBulkUpdateSourceAndSourcesMutation()[0];
  const [bulkDeleteSourceFilesFromIdMutation] =
    useBulkDeleteSourceFilesFromIdMutation();
  const getsourcefiles = useGetSuggestedDocumentsLazyQuery()[0];
  const { data: suggestedDocumentData } = useGetSuggestedDocumentsQuery({
    variables: {
      formId: formId,
      invitationId: invitationId,
      type: SourcesType.Uploaded?.dbTittle,
    },
  });
  const { data: companyDetails } = useGetCompanyDetailByIdQuery({
    variables: {
      id: pageLoadSourceFileData?.FormInvitation[0]?.companyId,
    },
  });
  const DropzoneCard = dynamic(
    () => import("@warp/web/pages/embed/AIBasedSections/Common/CustomDropzone"),
    {
      ssr: false,
    }
  );

  const [AICardData, setAICardData] = useState<AIDataCardsType>(AIFormCardData);
  const [headingTextData, setHeadingTextData] = useState<{
    mainHeading: string;
    subHeading: string;
  }>({
    mainHeading: "",
    subHeading: "",
  });
  const [docWithAI, setDocWithAI] = useState<boolean>(false);
  useEffect(() => {
    setAICardData(AIFormCardData);
  }, [AIFormCardData]);
  useEffect(() => {
    setDocWithAI(AIData.length > 0 ? AIData[0].docWithAI : false);
  }, [AIData]);
  const getHeading = (formName: string, datapercent: number) => {
    let HeadingTextData = {
      mainHeading: "",
      subHeading: "",
    };
    if (datapercent == 0) {
      HeadingTextData = {
        mainHeading: `Uploaded documents were processed for ${formName} but our AI couldn't capture any data.`,
        subHeading: "Please review and re-upload the suggested documents.",
      };
    } else if (datapercent > 0 && datapercent < 10) {
      HeadingTextData = {
        mainHeading: `Your documents have been processed for ${formName} and our AI has captured some data points.`,
        subHeading:
          "Upload more documents to automatically capture additional data and save effort.",
      };
    }
    return HeadingTextData;
  };
  useEffect(() => {
    const dataPointPercent =
      !!AICardData.dataCapturedUsingAI && !!AICardData.totalInputsRequired
        ? AICardData.dataCapturedUsingAI == 0 ||
          AICardData.totalInputsRequired == 0
          ? 0
          : Math.ceil(
              (Number(AICardData.dataCapturedUsingAI) * 100) /
                Number(AICardData.totalInputsRequired)
            )
        : 0;
    const formName =
      !!pageLoadSourceFileData?.FormInvitation &&
      pageLoadSourceFileData?.FormInvitation.length > 0
        ? pageLoadSourceFileData?.FormInvitation[0]?.Form?.name
        : "";
    let HeadingTextData = {
      mainHeading: "",
      subHeading: "",
    };
    ///for First Time with no suggestion for DOCWITHAI and first for ONLYDOC or have only fil upload with Uploaded Status
    if (
      pageLoadSourceFileData?.FormInvitation[0]?.AIBulkDocumentProcessings
        ?.length == 0
    ) {
      if (dataPointPercent == 0) {
        HeadingTextData = {
          mainHeading: `Let's start capturing your ${formName} data!`,
          subHeading: `Upload the suggested documents${
            docWithAI ? " to" : ", and our AI will"
          } automatically capture data for your assessment.`,
        };
      } else {
        HeadingTextData = {
          mainHeading: docWithAI
            ? `Great going! About ${dataPointPercent}% of ${formName} is ready for submission. These data points are captured through documents and links available on the internet.`
            : `Let's start capturing your ${formName} data!`,
          subHeading: docWithAI
            ? `Kindly upload suggested documents for us to capture additional data inputs. `
            : `Upload the suggested documents, and our AI will automatically capture data for your assessment.`,
        };
      }
    } else if (
      ///for Second Time with some uploaded files have success and some have Error status
      pageLoadSourceFileData?.Sources?.filter(
        (dataItems) =>
          dataItems?.type == SourcesType.Uploaded?.dbTittle &&
          isURLValid(String(dataItems?.SourceFile?.originalFileUrl)) &&
          dataItems?.SourceFile?.status == SourceFilesStatus?.Error
      ).length > 0 &&
      pageLoadSourceFileData?.Sources?.filter(
        (dataItems) =>
          dataItems?.type == SourcesType.Uploaded?.dbTittle &&
          dataItems?.SourceFile?.status == SourceFilesStatus?.Success
      ).length > 0
    ) {
      if (dataPointPercent >= 10) {
        HeadingTextData = {
          mainHeading: `Your ${formName} is now ${dataPointPercent}% complete.`,
          subHeading:
            "To capture additional data, review and re-upload the failed documents highlighted below.",
        };
      } else {
        if (docWithAI) {
          HeadingTextData = getHeading(formName, dataPointPercent);
        } else {
          if (dataPointPercent == 0) {
            HeadingTextData = {
              mainHeading: `Some of your documents were processed for ${formName}, but our AI couldn't capture any data.`,
              subHeading:
                "Please review and re-upload the suggested documents.",
            };
          } else if (dataPointPercent > 0 && dataPointPercent < 10) {
            HeadingTextData = {
              mainHeading: `Some of your documents have been processed for ${formName} and our AI has captured few data points.`,
              subHeading:
                "Please review and re-upload the failed documents to automatically capture additional data and save effort.",
            };
          }
        }
      }
    } else if (
      ///for Second Time with All uploaded files having success status
      pageLoadSourceFileData?.Sources?.filter(
        (dataItems) =>
          dataItems?.type == SourcesType.Uploaded?.dbTittle &&
          isURLValid(String(dataItems?.SourceFile?.originalFileUrl)) &&
          dataItems?.SourceFile?.status == SourceFilesStatus?.Error
      ).length == 0 &&
      pageLoadSourceFileData?.Sources?.filter(
        (dataItems) =>
          dataItems?.type == SourcesType.Uploaded?.dbTittle &&
          dataItems?.SourceFile?.status == SourceFilesStatus?.Success
      ).length > 0
    ) {
      if (dataPointPercent >= 10) {
        HeadingTextData = {
          mainHeading: `Great job! Your ${formName} is now ${dataPointPercent}% complete! All your recent documents have been processed successfully.`,
          subHeading:
            "Upload more documents to automatically capture additional data and save effort.",
        };
      } else {
        HeadingTextData = getHeading(formName, dataPointPercent);
      }
    } else if (
      ///for Second Time with All uploaded files having error status
      pageLoadSourceFileData?.Sources?.filter(
        (dataItems) =>
          dataItems?.type == SourcesType.Uploaded?.dbTittle &&
          isURLValid(String(dataItems?.SourceFile?.originalFileUrl)) &&
          dataItems?.SourceFile?.status == SourceFilesStatus?.Error
      ).length > 0 &&
      pageLoadSourceFileData?.Sources?.filter(
        (dataItems) =>
          dataItems?.type == SourcesType.Uploaded?.dbTittle &&
          dataItems?.SourceFile?.status == SourceFilesStatus?.Success
      ).length == 0
    ) {
      if (dataPointPercent >= 10) {
        HeadingTextData = {
          mainHeading: `Your ${formName} is still ${dataPointPercent}% complete.`,
          subHeading:
            "It appears your recently uploaded documents failed to process. Please review and re-upload them to capture additional data.",
        };
      } else {
        HeadingTextData = {
          mainHeading: `Your recently uploaded documents failed to process for ${formName}.`,
          subHeading:
            "Please review and re-upload them to automatically capture data.",
        };
      }
    } else if (
      ////for All Success Scenario with 0 datapoint capture
      pageLoadSourceFileData?.FormInvitation[0]?.AIBulkDocumentProcessings
        ?.length > 0
    ) {
      //// For second time user but when user have deleted all processed files
      if(docWithAI){
        if((pageLoadSourceFileData?.Sources?.filter(
          (dataItems) => dataItems?.type == SourcesType.Uploaded?.dbTittle).length == 0)
          ||
          (pageLoadSourceFileData?.Sources?.filter(
            (dataItems) =>
              dataItems?.type == SourcesType.Uploaded?.dbTittle &&
              dataItems?.SourceFile?.status == SourceFilesStatus?.Uploaded
          ).length == pageLoadSourceFileData?.Sources?.filter(
            (dataItems) => dataItems?.type == SourcesType.Uploaded?.dbTittle).length)
        ){
          if(dataPointPercent >= 10){
            HeadingTextData = {
              mainHeading: `Great job! Your ${formName} is now ${dataPointPercent}% complete!`,
              subHeading:
                "Upload the suggested documents to automatically capture data for your assessment.",
            }
          }else if (dataPointPercent > 0 && dataPointPercent < 10) {
            HeadingTextData = {
              mainHeading: `Some ${formName} data points have already been captured.`,
              subHeading:
                "Upload the suggested documents to automatically capture more data for your assessment.",
            }
          } else {
            HeadingTextData = {
              mainHeading: `Let's start capturing your ${formName} data!`,
              subHeading:
                "Upload the suggested documents to automatically capture data for your assessment.",
            }
          }
        }
      }else{
        if(dataPointPercent == 0){
          HeadingTextData = {
            mainHeading: `Uploaded documents were processed for ${formName} but our AI couldn't capture any data. `,
            subHeading: "Please review and re-upload the suggested documents.",
          };
        }
      }
    }
    if(HeadingTextData.mainHeading == "" || HeadingTextData.subHeading == ""){
      HeadingTextData = {
        mainHeading: `Let's start capturing your ${formName} data!`,
        subHeading:
          "Upload the suggested documents to automatically capture data for your assessment.",
      }
    }
    setHeadingTextData(HeadingTextData);
  }, [AICardData, docWithAI]);
  const [dropzoneConfig, setDropzoneConfig] = useState<SuggestedDocument[]>([]);
  useEffect(() => {
    if (suggestedDocumentData?.SuggestedDocuments) {
      const suggestedDocs: SuggestedDocument[] = [];
      suggestedDocumentData.SuggestedDocuments.forEach((doc) => {
        const sourceFileData = suggestedDocumentData?.Sources.filter(
          (items) => items.SourceFile?.suggestedDocumentId == doc.id
        );
        let cardSeq = doc.seqIndex;
        if (doc?.isOther) {
          sourceFileData.forEach((item, index) => {
            suggestedDocs.push({
              maxSize: doc.maxSize as number,
              sampleFileUrl: doc.sampleFileUrl as string,
              seqIndex: (doc.seqIndex + index) as number,
              title: item?.SourceFile?.originalFileName
                ? formatFileName(String(item?.SourceFile?.originalFileName))
                : "File Not Found",
              acceptedFormats: doc?.acceptedFormats,
              isOther: doc?.isOther,
              id: customDropzoneIdPrefix + index,
              dataPoints:
                !!item?.SuggestionSourceMappings_aggregate &&
                !!item?.SuggestionSourceMappings_aggregate?.aggregate?.count
                  ? item?.SuggestionSourceMappings_aggregate?.aggregate?.count
                  : 0,
              warning: item?.SourceFile?.error?.warning,
              FileProcessingErrorMessage: item?.SourceFile?.error?.error,
              sourceFileUrl:
                !!sourceFileData &&
                !!sourceFileData[0]?.SourceFile?.originalFileUrl
                  ? sourceFileData[0]?.SourceFile?.originalFileUrl
                  : "",
              copiedFromSourceId: sourceFileData[0].copiedFromSourceId,
            });
          });
          cardSeq = doc.seqIndex + sourceFileData.length;
        }
        suggestedDocs.push({
          id: doc.id as string,
          maxSize: doc.maxSize as number,
          sampleFileUrl: doc.sampleFileUrl as string,
          seqIndex: doc?.isOther ? cardSeq : doc.seqIndex,
          title: doc.title as string,
          acceptedFormats: doc?.acceptedFormats,
          isOther: doc?.isOther,
          dataPoints:
            !!sourceFileData &&
            !!sourceFileData[0]?.SuggestionSourceMappings_aggregate &&
            !!sourceFileData[0]?.SuggestionSourceMappings_aggregate?.aggregate
              ?.count
              ? sourceFileData[0]?.SuggestionSourceMappings_aggregate?.aggregate
                  ?.count
              : 0,
          warning: !doc?.isOther
            ? !!sourceFileData &&
              !!sourceFileData[0]?.SourceFile?.error?.warning
              ? sourceFileData[0]?.SourceFile?.error?.warning
              : ""
            : "",
          FileProcessingErrorMessage:
            !!sourceFileData && !!sourceFileData[0]?.SourceFile?.error?.warning
              ? sourceFileData[0]?.SourceFile?.error?.error
              : "",
          sourceFileUrl: !doc?.isOther
            ? !!sourceFileData &&
              !!sourceFileData[0]?.SourceFile?.originalFileUrl
              ? sourceFileData[0]?.SourceFile?.originalFileUrl
              : ""
            : "",
          copiedFromSourceId: sourceFileData[0]?.copiedFromSourceId,
        });
      });

      // Sort by seqIndex
      setDropzoneConfig(suggestedDocs.sort((a, b) => a.seqIndex - b.seqIndex));
    }
  }, [suggestedDocumentData]);

  const [getTokendetails, setTokendetails] = useState<{
    token?: string;
    serviceURL?: string;
  }>({});

  type UploadStatusMap = {
    [key in typeof dropzoneConfig[number]["id"]]: UploadStatus;
  };

  const initialUploadStatus: UploadStatusMap = dropzoneConfig.reduce(
    (acc, item) => ({ ...acc, [item.id]: DropzoneStatus.idle }), // Set initial status for each item as 'idle'
    {}
  );
  const [uploadStatus, setUploadStatus] =
    useState<UploadStatusMap>(initialUploadStatus);

  // ---- uploaded files state --------------------------------
  const [uploadedFiles, setUploadedFiles] = useState<{
    [key: string]: {
      file: File | null;
      metadata: any | undefined;
      fileUrl: string;
      sourceFileId: string;
      sourceId: string;
      isDeleted: boolean;
      status: string;
      FileProcessingErrorMessage: string;
    };
  }>({});
  const source_file_Insert_Input: SourceFiles_Insert_Input[] = [];

  const [ingestData, setIngestData] = useState<IngestFile[]>([]);
  const [sourceFileError, setSourceFileError] = useState<SourceFiles_Updates[]>(
    []
  );

  useEffect(() => {
    if (!getTokendetails.hasOwnProperty("token")) {
      postParentMessage(raraGetTokenDetails());
    }

    globalThis.addEventListener("message", async (event: any) => {
      event.preventDefault();
      let messageData: any;
      let dataType = typeof event.data;
      if (dataType === "string") {
        try {
          messageData = JSON.parse(event.data);
          const type = messageData.type;
          if (type === "snowkap-tokendetails") {
            setTokendetails({
              token: messageData.token,
              serviceURL: messageData.serviceurl,
            });
          }
        } catch (error) {}
      }
    });
  }, [getTokendetails]);

  useEffect(() => {
    globalThis.addEventListener("message", async (event: any) => {
      event.preventDefault();
      let messageData: any;
      let dataType = typeof event.data;
      if (dataType === "string") {
        try {
          messageData = JSON.parse(event.data);
          const type = messageData.type;
          if (type === "delete-file-from-modal-true") {
            await handleDelete(
              messageData.configId,
              messageData.sourceFileId,
              messageData.fileUrl,
              messageData.sourceId,
              messageData.submissionId,
              messageData.formId,
              messageData.cardArray,
              messageData.status
            );
          }
        } catch (error) {}
      }
    });
  }, []);

  useEffect(() => {
    let index: number = -1;
    pageLoadSourceFileData?.Sources?.filter(
      (items) => items.type == SourcesType?.Uploaded?.dbTittle
    )?.forEach(async (items) => {
      if (items?.SourceFile?.SuggestedDocument?.isOther == true) {
        index = index + 1;
      }
      const cardId =
        items?.SourceFile?.SuggestedDocument?.isOther == false
          ? items?.SourceFile?.suggestedDocumentId
          : customDropzoneIdPrefix + index;
      setUploadStatus((prevStatus) => ({
        ...prevStatus,
        [cardId]: (() => {
          switch (items?.SourceFile?.status) {
            case SourceFilesStatus.Success:
              return DropzoneStatus.complete;
            case SourceFilesStatus.Pending:
              return DropzoneStatus.pending;
            case SourceFilesStatus.Error:
              return DropzoneStatus.error;
            case SourceFilesStatus.Uploaded:
              return DropzoneStatus.uploaded;
            default:
              return DropzoneStatus.idle;
          }
        })(),
      }));
      let fileDetail: File;
      //if status is Error, expected no file present on s3, thus avoid calling urlToFile()
      if (items?.SourceFile?.status === SourceFilesStatus.Error) {
        fileDetail = new File(
          [new Blob()],
          String(items?.SourceFile?.originalFileName),
          {
            type: "",
            lastModified: Date.now(),
          }
        );
      } else {
        fileDetail = await urlToFile(
          String(items?.SourceFile?.originalFileUrl),
          String(items?.SourceFile?.originalFileName)
        );
      }
      setUploadedFiles((prevFiles) => ({
        ...prevFiles,
        [cardId]: {
          file: fileDetail,
          metadata: {
            Key: "public" + items?.SourceFile?.filePath,
            key: "public" + items?.SourceFile?.filePath,
            Bucket: String(items?.SourceFile?.originalFileUrl)
              .split("public")[0]
              .split("/")[
              String(items?.SourceFile?.originalFileUrl)
                .split("public")[0]
                .split("/").length - 1
            ],
            Location: items?.SourceFile?.originalFileUrl,
          },
          fileUrl: items?.SourceFile?.originalFileUrl,
          sourceFileId: items?.sourceFilesId,
          sourceId: items?.id,
          isDeleted: false,
          status: items.SourceFile?.status,
          FileProcessingErrorMessage: items?.SourceFile?.error?.error,
        },
      }));
    });
    //Set ingestion data on page load
    const updatesourceFile: SourceFiles_Updates[] =
      pageLoadSourceFileData?.Sources.filter(
        (items) =>
          items?.SourceFile?.status == SourceFilesStatus.Error &&
          isURLValid(String(items?.SourceFile?.originalFileUrl))
      ).map((dataItems) => {
        return {
          where: {
            id: {
              _eq: dataItems?.SourceFile?.id,
            },
          },
          _set: {
            error: {
              warning: dataItems?.SourceFile?.error?.warning,
              error: commonValues.commonError,
            },
          },
        };
      });
    setSourceFileError(updatesourceFile);
    setIngestData(
      pageLoadSourceFileData?.Sources?.filter(
        (items) => items.SourceFile?.status === SourceFilesStatus.Uploaded
      )?.map((items) => ({
        url: String(items?.SourceFile?.originalFileUrl),
        file_name: String(items?.SourceFile?.originalFileName),
        original_filename: String(items?.SourceFile?.originalFileName),
        source_id: String(items?.id),
      }))
    );
  }, [pageLoadSourceFileData?.Sources]);
  useEffect(() => {
    const updateError = async () => {
      if (sourceFileError.length > 0) {
        const updatedData = await updateSourceFiles({
          variables: {
            SourceFiles: sourceFileError,
            Sources: [],
          },
        });
      }
    };
    updateError();
  }, [sourceFileError]);
  /**
   * This useEffect hook monitors the upload status of files and sends messages to the parent window
   * to  button based on the processing state of the files.
   */
  useEffect(() => {
    const ingestDataReq: IngestData = {
      company_id: pageLoadSourceFileData?.FormInvitation[0]?.companyId,
      form_id: formId,
      form_invitation_id: invitationId,
      submission_id: submissionId,
      files: ingestData,
    };
    if (!!uploadStatus) {
      const allUploaded = Object.entries(uploadStatus).filter(
        ([key, value]) => value == DropzoneStatus.uploaded
      ).length;
      const allSuccess = Object.entries(uploadStatus).filter(
        ([key, value]) => value == DropzoneStatus.complete
      ).length;
      const allIdle = Object.entries(uploadStatus).filter(
        ([key, value]) =>
          value == DropzoneStatus.idle ||
          value == DropzoneStatus.uploading ||
          value == DropzoneStatus.error
      ).length;
      postParentMessage(
        ProcessSkipAndContinue(
          allIdle == Object.entries(uploadStatus).length,
          allUploaded == 0 && allSuccess > 0,
          allUploaded > 0,
          ingestDataReq,
          docWithAI
        )
      );
    } else {
      postParentMessage(
        ProcessSkipAndContinue(
          invitationSourceItem.length == 0,
          invitationUploadedDocument.length == 0,
          invitationUploadedDocument.length > 0,
          ingestDataReq,
          docWithAI
        )
      );
    }
    postParentMessage(hideprocessButton(false));
  }, [uploadStatus, ingestData, docWithAI]);
  const setFileStatus = async (configId: string) => {
    const loaddata = await getsourcefiles({
      variables: {
        formId: formId,
        invitationId: invitationId,
        type: SourcesType.Uploaded?.dbTittle,
      },
      fetchPolicy: "no-cache",
    });

    //Update the card data with the total data points added from the source data, after completion of file processing
    setDropzoneConfig((prevConfig) => {
      const dropzoneConfigData = prevConfig.map((items) => {
        if (items?.id == configId) {
          const sourceData = loaddata?.data?.Sources.filter(
            (item) => item.SourceFile?.originalFileUrl == items?.sourceFileUrl
          );
          return {
            ...items,
            dataPoints: Number(
              !!sourceData &&
                sourceData?.length > 0 &&
                !!sourceData[0]?.SuggestionSourceMappings_aggregate?.aggregate
                  ?.count
                ? sourceData[0]?.SuggestionSourceMappings_aggregate?.aggregate
                    ?.count
                : 0
            ),
          };
        } else {
          return {
            ...items,
          };
        }
      });
      return dropzoneConfigData.sort((a, b) => a.seqIndex - b.seqIndex);
    });

    const AIcardData: AIDataCardsType = await getWebCurationData(
      formId,
      invitationId
    );
    setAICardData(AIcardData);
    setUploadStatus((prevStatus) => ({
      ...prevStatus,
      [configId]: "complete",
    }));
  };

  const handleInsertRejectedFiles = async (
    id: string,
    files: rejectedFiles[],
    anyotherDocuments: boolean
  ) => {
    const otherDocumentData: SuggestedDocument[] = [];
    const specificCardData = dropzoneConfig.filter((items) => items?.id == id);
    const source_file_Insert_Input = files.map((rejectedfile, index) => {
      const fileUrl = `${crypto.randomUUID()}-${id}`;
      return {
        originalFileName: rejectedfile?.file.name,
        originalFileUrl: crypto.randomUUID(),
        fileName: rejectedfile?.file?.name,
        filePath: crypto.randomUUID(),
        suggestedDocumentId: id,
        uploadedByUserId: userSession?.user?.id,
        fileSize: String(rejectedfile?.file?.size),
        error: { warning: "", error: rejectedfile?.rejectionMessage },
        status: rejectedfile?.rejectionMessage
          ? SourceFilesStatus.Error
          : SourceFilesStatus.Uploaded,
        Sources: {
          on_conflict: { constraint: Sources_Constraint.SourcesPkey },
          data: [
            {
              formInvitationId: invitationId,
              type: SourcesType.Uploaded?.dbTittle,
              url: fileUrl,
            },
          ],
        },
      };
    });

    try {
      const insertedFiles = await insertSourceFiles({
        variables: { data: source_file_Insert_Input },
      });

      const updatedUploadStatus = { ...uploadStatus };
      const updatedUploadedFiles = { ...uploadedFiles };

      (insertedFiles?.data?.insert_SourceFiles?.returning || [])?.forEach(
        (fileData, index) => {
          const otherDocId = `${new Date().getTime()}-${new Date().getMilliseconds()}-${Math.floor(
            Math.random() * 1000000
          )}`;
          const fileDetails = files.find(
            (items) =>
              formatFileName(items?.file?.name) ==
              formatFileName(fileData?.originalFileName)
          );
          if (anyotherDocuments) {
            otherDocumentData.push({
              title: formatFileName(String(fileData?.fileName)),
              sampleFileUrl: specificCardData[0].sampleFileUrl,
              acceptedFormats: specificCardData[0].acceptedFormats,
              maxSize: specificCardData[0].maxSize,
              isOther: anyotherDocuments,
              id: otherDocId,
              seqIndex: specificCardData[0].seqIndex + index,
              dataPoints: 0,
              warning: "",
              FileProcessingErrorMessage: "",
              sourceFileUrl: "",
            });
          }
          updatedUploadStatus[anyotherDocuments ? otherDocId : id] =
            DropzoneStatus.error as UploadStatus;
          updatedUploadedFiles[anyotherDocuments ? otherDocId : id] = {
            file: fileDetails?.file as File,
            metadata: "",
            fileUrl: String(fileData?.originalFileUrl),
            sourceFileId: fileData?.id,
            sourceId: fileData?.Sources?.[0]?.id,
            isDeleted: false,
            status: SourceFilesStatus.Error,
            FileProcessingErrorMessage: fileData?.error?.error,
          };
        }
      );
      if (anyotherDocuments) {
        for (let k = 0; k < specificCardData.length; k++) {
          specificCardData[k].seqIndex =
            specificCardData[k].seqIndex + otherDocumentData.length;
        }
        setDropzoneConfig((prevConfig) =>
          [...prevConfig, ...otherDocumentData].sort(
            (a, b) => a.seqIndex - b.seqIndex
          )
        );
      }
      setUploadStatus(updatedUploadStatus);
      setUploadedFiles(updatedUploadedFiles);
    } catch (error) {
      console.error("Error inserting source files:", error);
    }
  };

  const handleDrop = async (
    id: string,
    files: File[],
    isOther: boolean,
    docTitle: string
  ) => {
    let alldropzoneConfig = dropzoneConfig;
    const folderName = `AI_SOURCES/${pageLoadSourceFileData?.FormInvitation[0]?.companyId}`;
    const source_file_Updates: SourceFiles_Updates[] = [];
    const source_Updates: Sources_Updates[] = [];
    const uploadedFilesStateData: { [key: string]: UploadFileStatData } = {};
    const otherDocumentData: SuggestedDocument[] = [];
    let insertedData: SourceFile[] = [];
    let insertError: string | unknown;
    const FilewithIdArray: any[] = [];

    // Update 'uploading' status for non-other documents
    const uploadCardStatus = uploadStatus;
    if (!isOther) {
      uploadCardStatus[id] = DropzoneStatus.uploading as UploadStatus;
      files.forEach((file) => {
        FilewithIdArray.push({
          id: id,
          file: file,
        });
      });
    } else {
      // Handle other documents
      const otherDocData = dropzoneConfig.filter((items) => items.id == id);
      files.forEach((file, index) => {
        const otherDocId = `${new Date().getTime()}-${new Date().getMilliseconds()}-${Math.floor(
          Math.random() * 1000000
        )}`;
        otherDocumentData.push({
          title: formatFileName(String(file.name)),
          sampleFileUrl: otherDocData[0].sampleFileUrl,
          acceptedFormats: otherDocData[0].acceptedFormats,
          maxSize: otherDocData[0].maxSize,
          isOther: isOther,
          id: otherDocId,
          seqIndex: otherDocData[0].seqIndex + index,
          dataPoints: 0,
          warning: "",
          FileProcessingErrorMessage: "",
          sourceFileUrl: "",
        });
        FilewithIdArray.push({
          id: otherDocId,
          file: file,
        });
      });

      for (let k = 0; k < otherDocData.length; k++) {
        otherDocData[k].seqIndex =
          otherDocData[k].seqIndex + otherDocumentData.length;
      }

      setDropzoneConfig((prevConfig) =>
        [...prevConfig, ...otherDocumentData].sort(
          (a, b) => a.seqIndex - b.seqIndex
        )
      );
      otherDocumentData.forEach((item) => {
        uploadCardStatus[item.id] = DropzoneStatus.uploading as UploadStatus;
      });
    }
    setUploadStatus((prevStatus) => ({
      ...prevStatus,
      ...uploadCardStatus,
    }));
    //Structure of source_file_Insert_Input before uploading file to S3
    files?.forEach((file, index) => {
      const uniqueId = `${crypto.randomUUID()}-${index}`;
      source_file_Insert_Input.push({
        originalFileName: file.name,
        originalFileUrl: uniqueId,
        fileName: uniqueId,
        filePath: uniqueId,
        suggestedDocumentId: id,
        uploadedByUserId: userSession?.user?.id,
        fileSize: String(file.size),
        error: { warning: "", error: "" },
        Sources: {
          on_conflict: { constraint: Sources_Constraint.SourcesPkey },
          data: [
            {
              formInvitationId: invitationId,
              type: SourcesType.Uploaded?.dbTittle,
              url: uniqueId,
            },
          ],
        },
      });
    });

    // Insert sample file to DB
    try {
      const insertResult = await insertSourceFiles({
        variables: { data: source_file_Insert_Input },
      });
      insertedData = insertResult?.data?.insert_SourceFiles?.returning || [];
    } catch (error) {
      insertError = error;
      console.error("Error inserting source files:", error);
    }

    //Handle file uploads concurrently
    const uploadPromises = FilewithIdArray.map(async (fileItem, index) => {
      // Upload the actual file to S3
      const { fileInfo: fileResult, error: errorMessage } = await uploadFile(
        isOther ? fileItem?.id : id,
        fileItem?.file,
        false,
        folderName
      );

      let fileUploadError: string = !!errorMessage
        ? "File upload failed. Please try again later."
        : "";

      let validationApiResponse: any;
      let raraMessage: string = "";
      let fileProcessError: string = !!fileUploadError
        ? fileUploadError
        : insertError
        ? "An unexpected error occurred while saving the file. Please try again later."
        : "";

      if (!errorMessage) {
        //#region RARA STarts

        //Handle validation API asynchronously
        if (fileResult?.type == "pdf") {
          validationApiResponse = await axios
            .post(
              getTokendetails.serviceURL + "warp/document-validation",
              JSON.stringify({
                document_url: String(fileResult?.path),
                document_name: isOther
                  ? formatFileName(String(fileResult?.name))
                  : docTitle,
                company_name: companyDetails?.Company[0]?.name,
                parentCompanies: [],
              }),
              {
                headers: {
                  Authorization: "Bearer " + getTokendetails.token,
                  "Content-Type": "application/json",
                },
              }
            )
            .then((res) => res)
            .catch((err) => err);
        }

        //validation response (runs in parallel for each file)
        if (validationApiResponse) {
          try {
            if (
              validationApiResponse?.data?.valid?.toString()?.toLowerCase() ===
              "false"
            ) {
              raraMessage = validationApiResponse?.data?.explanation;
            }
          } catch (error) {
            raraMessage = `Invalid ${
              isOther ? formatFileName(String(fileResult?.name)) : docTitle
            } file`;
          }
        }
        //#endregion
      }

      let uploadedFilePath: string =
        folderName +
        fileResult?.path.split(folderName)[
          fileResult?.path.split(folderName).length - 1
        ];
      uploadedFilesStateData[isOther ? fileItem?.id : id] = {
        file: fileItem?.file,
        metadata: fileResult?.metadata,
        fileUrl: String(fileResult?.path),
        sourceFileId: "",
        warning: raraMessage,
        FileProcessingErrorMessage: fileProcessError,
      };
      setUploadStatus((prevStatus) => ({
        ...prevStatus,
        [isOther ? fileItem?.id : id]: (fileUploadError || insertError
          ? DropzoneStatus.error
          : DropzoneStatus.uploaded) as UploadStatus,
      }));
      //Update query payload for source_files and sources
      const sourceFileData = insertedData?.filter(
        (item: { originalFileName: string }) =>
          formatFileName(item?.originalFileName) ===
          formatFileName(files[index].name)
      );

      if (!!sourceFileData && sourceFileData.length > 0) {
        const updateObject = errorMessage
          ? {
              status: SourceFilesStatus.Error,
              error: { warning: raraMessage, error: fileProcessError },
            }
          : {
              originalFileUrl: fileProcessError ? "" : fileResult?.path,
              fileName: fileProcessError
                ? ""
                : fileResult?.path.split("/").pop() || "",
              filePath: uploadedFilePath ? uploadedFilePath : "",
              error: { warning: raraMessage, error: fileProcessError },
              status: fileProcessError
                ? SourceFilesStatus.Error
                : SourceFilesStatus.Uploaded,
            };

        source_file_Updates.push({
          where: {
            id: { _eq: sourceFileData[0]?.id },
          },
          _set: updateObject,
        });

        // Only add to source_Updates if errorMessage is NOT present
        if (!errorMessage) {
          source_Updates.push({
            where: {
              id: { _eq: sourceFileData[0]?.Sources[0]?.id },
            },
            _set: {
              url: uploadedFilePath,
            },
          });
        }
      }
    });
    // Execute all uploads and validation in parallel
    await Promise.allSettled(uploadPromises);
    // allows all tasks to complete and reports success/failure individually.
    // Update dropzone configuration with new file data
    setDropzoneConfig((prevConfig) => {
      const dropzoneConfigData = prevConfig.map((items) => {
        return {
          ...items,
          sourceFileUrl: !!uploadedFilesStateData[items.id]
            ? uploadedFilesStateData[items.id].fileUrl
            : items?.sourceFileUrl,
          warning: !!uploadedFilesStateData[items.id]
            ? uploadedFilesStateData[items.id].warning
            : items?.warning,
          FileProcessingErrorMessage: !!uploadedFilesStateData[items.id]
            ? uploadedFilesStateData[items.id].FileProcessingErrorMessage
            : items?.FileProcessingErrorMessage,
        };
      });
      return dropzoneConfigData.sort((a, b) => a.seqIndex - b.seqIndex);
    });
    const removeProgressFromLocalStorage = (key: string) => {
      if (typeof window !== "undefined") {
        const progress = localStorage.getItem(key);
        if (progress === "100") {
          localStorage.removeItem(key);
          window.dispatchEvent(new Event("storage"));
        }
      }
    };
    FilewithIdArray.forEach((items) => {
      removeProgressFromLocalStorage(`uploadProgress_${items?.id}`);
    });
    // Update the source files into the DB
    const updatedData = await updateSourceFiles({
      variables: {
        SourceFiles: source_file_Updates,
        Sources: source_Updates,
      },
    });

    if (!!updatedData?.data) {
      // Update the uploadedFiles state with the inserted data
      Object.keys(uploadedFilesStateData).forEach((uploadFileItem) => {
        // Filter the inserted data to find the corresponding file data
        const fileData = !!updatedData?.data?.update_SourceFiles_many
          ? updatedData?.data?.update_SourceFiles_many?.filter(
              (items) =>
                items?.returning[0].originalFileUrl ==
                uploadedFilesStateData[uploadFileItem]?.fileUrl
            )
          : [];
        // Update the uploadedFiles state with the new file data
        setUploadedFiles((prevFiles) => ({
          ...prevFiles,
          [uploadFileItem]: {
            file: uploadedFilesStateData[uploadFileItem]?.file,
            metadata: uploadedFilesStateData[uploadFileItem]?.metadata,
            fileUrl: uploadedFilesStateData[uploadFileItem]?.fileUrl,
            sourceFileId: String(
              fileData.length > 0 ? fileData[0]?.returning[0]?.id : ""
            ),
            sourceId: String(
              fileData.length > 0
                ? fileData[0]?.returning[0]?.Sources[0]?.id
                : ""
            ),
            isDeleted: false,
            status: String(
              fileData.length > 0 ? fileData[0]?.returning[0]?.status : ""
            ),
            FileProcessingErrorMessage: "",
          },
        }));
      });
      // Prepare the data for ingestion
      if (
        !!updatedData?.data?.update_SourceFiles_many &&
        updatedData?.data?.update_SourceFiles_many.length > 0
      ) {
        const injestArray = updatedData?.data?.update_SourceFiles_many?.map(
          (items) => {
            return {
              url: String(items?.returning[0]?.originalFileUrl),
              file_name: String(items?.returning[0]?.fileName),
              original_filename: String(items?.returning[0]?.originalFileName),
              source_id: String(items?.returning[0]?.Sources[0]?.id),
            };
          }
        );
        // If there are files to ingest, send them to the AI processing API
        if (!!injestArray && injestArray?.length > 0) {
          setIngestData((prevData: IngestFile[]) => [
            ...prevData,
            ...injestArray,
          ]);
        }
      }
    }
  };

  const handleDelete = async (
    configId: string,
    sourceFileId: string,
    fileUrl: string,
    sourceId: string,
    submissionId: string,
    formId: string,
    cardArray: SuggestedDocument[],
    status: string
  ) => {
    let invitationPercentage: invitationCompletion[] = [];
    if (!status) return;

    // Update the uploaded files state to mark the file as deleted
    setUploadedFiles((prevFiles) => ({
      ...prevFiles,
      [configId]: {
        ...prevFiles[configId],
        isDeleted: true,
      },
    }));

    if (isURLValid(fileUrl)) {
      let isExecuteDb: boolean = false;
      if (status == SourceFilesStatus.Uploaded) {
        // Move the file to a different location in S3
        fetch(process.env.NEXT_PUBLIC_API_BASE_URL + "/api/awss3/move-file", {
          method: "POST",
          headers: {
            "content-type": "application/json",
          },
          body: JSON.stringify({
            filePath: fileUrl,
          }),
        }).then((response) => {
          return response.json();
        });
        bulkDeleteSourceFilesFromIdMutation({
          variables: { id: [sourceFileId] },
        }).catch((error) => {
          console.error("Error deleting file:", error);
        });
      } else {
        await fetch(
          process.env.NEXT_PUBLIC_API_BASE_URL + "/api/AI/AIprocessing",
          {
            method: "POST",
            headers: {
              "content-type": "application/json",
            },
            body: JSON.stringify({
              process: "deleteFile",
              data: [
                {
                  source_id: sourceId,
                  source_files_id: sourceFileId,
                  form_invitation_id: invitationId,
                  form_submission_id: submissionId,
                },
              ],
            }),
          }
        )
          .then((response) => {
            if (response?.statusText == "OK" && response?.status == 200) {
              return response.json();
            }
          })
          .then(async (resultdata) => {
            if (resultdata?.data?.status == "success") {
              isExecuteDb = true;
              // Move the file to a different location in S3
              fetch(
                process.env.NEXT_PUBLIC_API_BASE_URL + "/api/awss3/move-file",
                {
                  method: "POST",
                  headers: {
                    "content-type": "application/json",
                  },
                  body: JSON.stringify({
                    filePath: fileUrl,
                  }),
                }
              ).then((response) => {
                return response.json();
              });
            }
          });
      }
      if (isExecuteDb) {
        // Update the AI card data
        const AIcardData: AIDataCardsType = await getWebCurationData(
          formId,
          invitationId
        );
        setAICardData(AIcardData);
        fetch(
          process.env.NEXT_PUBLIC_API_BASE_URL +
            "/api/AI/calculate-completion-percentage",
          {
            method: "POST",
            headers: {
              "content-type": "application/json",
            },
            body: JSON.stringify({
              invitationIdArray: [invitationId],
              isForuploadDocPage: true,
            }),
          }
        )
          .then((response) => {
            return response.json();
          })
          .then(async (resultdata) => {
            invitationPercentage = resultdata?.data;
          });
      }
    } else {
      bulkDeleteSourceFilesFromIdMutation({
        variables: { id: [sourceFileId] },
      }).catch((error) => {
        console.error("Error deleting file:", error);
      });
    }
    const cardDetail = cardArray.filter((item) => item.id == configId);
    // Remove the custom dropzone configuration if it exists
    if (cardDetail[0]?.isOther) {
      setDropzoneConfig(
        cardArray
          .filter((item) => item.id != configId)
          .sort((a, b) => a.seqIndex - b.seqIndex) as SuggestedDocument[]
      );
      delete uploadStatus[configId];
      delete uploadedFiles[configId];
    }
    // Reset the upload status and uploaded files state
    setUploadStatus((prevStatus) => ({
      ...prevStatus,
      [configId]: "idle",
    }));

    setDropzoneConfig((prevStatus) =>
      prevStatus.map((item) =>
        item.id === configId ? { ...item, warning: "" } : item
      )
    );
    setUploadedFiles((prevFiles) => ({
      ...prevFiles,
      [configId]: {
        file: null,
        metadata: null,
        fileUrl: "",
        sourceFileId: "",
        sourceId: "",
        isDeleted: false,
        status: "",
        FileProcessingErrorMessage: "",
      },
    }));
    //remove element from ingestData
    setIngestData(ingestData.filter((items) => items?.source_id != sourceId));
    postParentMessage(calculatePercentage());
  };

  const [showAll, setShowAll] = useState(false);
  const [viewClick, setViewClick] = useState(false);

  useEffect(() => {
    setShowAll(
      viewClick ? true : dropzoneConfig.length > dataInFirstRow ? false : true
    );
  }, [dropzoneConfig.length]);

  const toggleShowAll = () => {
    setShowAll((prev) => !prev);
    setViewClick((prev) => !prev);
  };

  return dropzoneConfig.length === 0 ? (
    <Spinner visible={true} />
  ) : (
    <>
      <Container fluid px={5} bg="transparent">
        <Box>
          <Space h={20} />
          <InfoItem
            c="#122F47"
            fz="30px"
            lh="38.04px"
            fw={400}
            label={headingTextData?.mainHeading}
            align="left"
          />
          <Space h={35} />
          <InfoItem
            c="#122F47"
            fz="20px"
            lh="38.04px"
            fw={400}
            label={headingTextData?.subHeading}
            align="left"
          />
          <Space h={25} />
          <SimpleGrid
            spacing="sm"
            m="0"
            breakpoints={[
              {
                minWidth: "xs",
                cols: 1,
                spacing: "xl",
                verticalSpacing: "xl",
              },
              {
                minWidth: "sm",
                cols: 2,
                spacing: "xl",
                verticalSpacing: "xl",
              },
              {
                minWidth: "md",
                cols: 4,
                spacing: 32,
                verticalSpacing: 32,
              },
            ]}
          >
            {dropzoneConfig
              .slice(0, showAll ? dropzoneConfig.length : dataInFirstRow)
              .map((config) => (
                <DropzoneCard
                  key={config.id}
                  id={config.id}
                  heading={config.title}
                  sampleLink={config?.sampleFileUrl!}
                  files={
                    uploadedFiles[config.id]?.file
                      ? [
                          {
                            name: uploadedFiles[config.id]?.file?.name!,
                            size: uploadedFiles[config.id]?.file?.size!,
                            status: uploadStatus[config.id],
                            metadata: uploadedFiles[config.id].metadata,
                            sourceId: uploadedFiles[config.id].sourceId,
                          },
                        ]
                      : []
                  }
                  maxSize={config.maxSize}
                  acceptedFormats={config.acceptedFormats.acceptedFormats}
                  uploadStatus={uploadStatus[config.id] || "idle"}
                  onDrop={(files) =>
                    handleDrop(config.id, files, config.isOther, config.title)
                  }
                  onComplete={() => {
                    setFileStatus(config.id);
                  }}
                  onDelete={() => {
                    postParentMessage(
                      deleteFileModal(
                        config.id,
                        uploadedFiles[config.id]?.fileUrl,
                        uploadedFiles[config.id]?.sourceFileId,
                        uploadedFiles[config.id]?.sourceId,
                        submissionId,
                        formId,
                        dropzoneConfig,
                        uploadedFiles[config.id]?.status
                      )
                    );
                  }}
                  isDeleting={uploadedFiles[config.id]?.isDeleted}
                  anyotherDocuments={config.isOther}
                  dataPoint={config.dataPoints}
                  warningMessage={config.warning}
                  FileProcessingErrorMessage={
                    uploadedFiles[config.id]?.FileProcessingErrorMessage
                      ? uploadedFiles[config.id]?.FileProcessingErrorMessage
                      : config.FileProcessingErrorMessage
                  }
                  insertRejectedFiles={(file, anyotherDocuments) => {
                    handleInsertRejectedFiles(
                      config.id,
                      file,
                      anyotherDocuments
                    );
                  }}
                  autoFetched={!!config.copiedFromSourceId} //Just for testing
                />
              ))}
          </SimpleGrid>
          {dropzoneConfig.length > dataInFirstRow ? (
            <Center>
              <Button
                variant="outline"
                color="#003B52"
                c="#003B52"
                px="20px"
                fz="12px"
                lh="16px"
                fw={700}
                lts="1.5px"
                radius={"xl"}
                mt="xl"
                styles={{
                  root: {
                    borderColor: "#122F47",
                    "&:hover": {
                      backgroundColor: "#eeeeee",
                    },
                  },
                }}
                onClick={toggleShowAll}
              >
                {showAll
                  ? "SHOW LESS"
                  : `VIEW ALL ${dropzoneConfig.length} SUGGESTED DOCUMENTS`}
              </Button>
            </Center>
          ) : (
            <></>
          )}
        </Box>
      </Container>
    </>
  );
};

export default UploadDocuments;
