import { sdk } from "@warp/graphql/generated/server";
import { AiBulkDocumentProcessing_Updates } from "@warp/graphql/generated/types";
import {
  AIBulkProcessingCompleteEmail,
  AIEmailTemplates,
  bulkFileCurationStatus,
  emailSourceFileDetails,
  inputFieldsinFormFields,
} from "@warp/shared/constants/app.constants";
import { uploadError } from "../aws-s3.service";
import { sendFileCurationCompletionEmail } from "../notification.service";
import { calculateCompletionPercentage } from "./calculate-completion-percentage";

export const documentProcessingCompleted = async (
  bulkProcessingId: string[]
) => {
  let sendEmailResponse: any = [];
  try {
    const bulkProcessingUpdates: AiBulkDocumentProcessing_Updates[] = [];
    const processingData = await sdk.getProcessingDataById({
      Id: bulkProcessingId,
      inputFields: inputFieldsinFormFields,
    });
    let allSourceIdArray: string[] = [];
    const invitationData = processingData?.AIBulkDocumentProcessing?.map(
      (items) => {
        allSourceIdArray = [
          ...allSourceIdArray,
          ...items?.processedDocuments.map(
            (documentItems: Record<string, string>) => documentItems?.sourceId
          ),
        ];
        return {
          id: items?.id,
          invitationId: items?.formInvitationId,
          allSourceIds: items?.processedDocuments.map(
            (documentItems: Record<string, string>) => documentItems?.sourceId
          ),
          answerRecord:
            !!items?.FormInvitation?.FormSubmissions.filter(
              (dataItems) => dataItems?.Answers?.length > 0
            ) &&
            items?.FormInvitation?.FormSubmissions.filter(
              (dataItems) => dataItems?.Answers?.length > 0
            ).length > 0
              ? items?.FormInvitation?.FormSubmissions.filter(
                  (dataItems) => dataItems?.Answers?.length > 0
                )[0]?.Answers
              : [],
          invitationFormDetails: items?.FormInvitation?.Form,
        };
      }
    );
    const sourceProcessingData = await sdk.getSourceDataById({
      sourceId: allSourceIdArray,
    });
    const emailDetails: AIBulkProcessingCompleteEmail[] = [];
    invitationData.forEach((items) => {
      const allSourceIdStatus = sourceProcessingData?.Sources.filter(
        (sourceItems) =>
          items?.allSourceIds.find(
            (documentItems: string) => documentItems == sourceItems?.id
          )
      );
      const FileDetailsList = allSourceIdStatus?.map((sourcesItem) => {
        return {
          fileName: String(sourcesItem?.SourceFile?.originalFileName),
          Status: String(sourcesItem?.SourceFile?.status),
          fileUrl: String(sourcesItem?.SourceFile?.originalFileUrl),
          documentName: String(
            sourcesItem?.SourceFile?.SuggestedDocument?.isOther
              ? "Other Document"
              : sourcesItem?.SourceFile?.SuggestedDocument?.title
          ),
        };
      }) as emailSourceFileDetails[];
      emailDetails.push({
        emailType: [AIEmailTemplates.AIDocumentProcessed],
        FileDetails: FileDetailsList,
        invitationId: items?.invitationId,
      });
      const processedDocumentsArray = allSourceIdStatus?.map((sourceItems) => {
        return {
          sourceId: sourceItems?.id,
          status: sourceItems?.SourceFile?.status,
        };
      });
      bulkProcessingUpdates.push({
        where: {
          id: {
            _eq: items?.id,
          },
        },
        _set: {
          processedDocuments: processedDocumentsArray,
          emailSendAt: new Date(),
          updated_at: new Date(),
          requestStatus: bulkFileCurationStatus.Completed,
        },
      });
    });

    //#region Email Sending
    const response = await sendFileCurationCompletionEmail(emailDetails);
    if (response) {
      await sdk.bulkUpdateProcessingData({
        AIBulkDocumentProcessingUpdate: bulkProcessingUpdates,
      });
      await calculateCompletionPercentage(
        invitationData?.map((items) => items.invitationId),
        true
      );
      sendEmailResponse = response;
    }
    //#endregion
  } catch (error: any) {
    const errorContent = JSON.stringify({
      datetime: new Date().toISOString(),
      message: error.message,
      stack: error.stack,
    });
    await uploadError("exception-logs", "exception-logs", errorContent);
  }
  return sendEmailResponse;
};
