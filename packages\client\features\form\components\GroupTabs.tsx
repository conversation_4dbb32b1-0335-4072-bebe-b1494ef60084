import {
  Accordion,
  ActionIcon,
  Box,
  Button,
  createStyles,
  Flex,
  Group,
  MantineTheme,
  MediaQuery,
  Progress,
  Stack,
  Tabs,
  Text,
  Title,
  Tooltip,
  useMantineTheme,
} from "@mantine/core";
import {
  IconChevronDown,
  IconChevronLeft,
  IconChevronRight,
  IconCircle,
  IconPlus,
} from "@tabler/icons";
import { useUserSession } from "@warp/client/hooks/use-user-session";
import Spinner from "@warp/client/layouts/Spinner";
import { useBulkInsertAnswerMutation } from "@warp/graphql/mutations/generated/bulk-insert-answer";
import { useBulkInsertInterimAnswerMutation } from "@warp/graphql/mutations/generated/bulk-insert-intrim-answer";
import { useBulkUpdateInterimRecommendationByInterimAnswerIdMutation } from "@warp/graphql/mutations/generated/bulk-update-interim-recommendation";
import { useInsertRaraValidationAndRatingMutation } from "@warp/graphql/mutations/generated/insert-rara-validation-and-rating";
import { useUpsertValidationWarningLogsMutation } from "@warp/graphql/mutations/generated/Insert-validation-warning-logs";
import { useUpdateAnswerMutation } from "@warp/graphql/mutations/generated/update-answer";
import { useUpdateInterimAnswerByQuestionIdAndSubmissionIdMutation } from "@warp/graphql/mutations/generated/update-interim-answer-by-question-id-and-submission-id";
import { useUpsertAnswerMutation } from "@warp/graphql/mutations/generated/upsert-answer";
import { useUpsertFormInvitationCompetionMutation } from "@warp/graphql/mutations/generated/upsert-form-invitation-competion-by-invitation-id";
import { useGetFormFieldsByQuestionIdLazyQuery } from "@warp/graphql/queries/generated/get-form-fields-by-question-id";
import { useGetQuestionListByInvitationIdLazyQuery } from "@warp/graphql/queries/generated/get-question-list-by-invitationId";
import { useRaraCompanyAccessQuery } from "@warp/graphql/queries/generated/get-rara-features-access-companyid";
import { useGetRecomendationBySubmissionIdAndFormfieldIdLazyQuery } from "@warp/graphql/queries/generated/get-recomendation-by-submissionId-and-formfieldId";

// import { saveAs } from "file-saver";
import { useUpdateAssesseeUserMappingbyInvitationIdMutation } from "@warp/graphql/mutations/generated/update-assessee-user-mapping-by-invitationid";
import { useUpdateAssesseeUserMappingForResponderMutation } from "@warp/graphql/mutations/generated/update-assessee-user-mapping-for-responder";
import { useUpdateFormInvitationStatusMutation } from "@warp/graphql/mutations/generated/update-form-invitation-status";
import { useUpdateValidationWarningLogsMutation } from "@warp/graphql/mutations/generated/update-ValidationWarningLogs";
import { useGetAnswerByQuestionIdAndSubmissionIdLazyQuery } from "@warp/graphql/queries/generated/get-answer-by-questionid-and-submissionid";
import { useGetassesseeuserbyinvitationIdQuery } from "@warp/graphql/queries/generated/get-assesseeuser-by-invitationid";
//import { useGetCompanyDetailByIdLazyQuery } from "@warp/graphql/queries/generated/get-companydetail-by-id";
import { useGetformFieldsbySubmissionIdQuery } from "@warp/graphql/queries/generated/get-formfield-by-submissionid";
//import { useGetGlobalMasterByBrsrTemplateLazyQuery } from "@warp/graphql/queries/generated/get-global-master-by-brsrtemplate";
import { useGetInvitationAndSubmissionDetailsByInvitationIdQuery } from "@warp/graphql/queries/generated/get-invitation-and-submission-details-by-invitation-id";
//import { useGetRecommendationByInterimAnswerIdAndQuestionIdLazyQuery } from "@warp/graphql/queries/generated/get-recommendation-by-interimanswerid-and-questionid";
import { useBulkUpdateSuggestionsMutation } from "@warp/graphql/mutations/generated/bulk-update-suggestions";
import {
  AppRoles,
  FormInvitationStatus,
  FormMode,
  QuestionStatus,
  userInvitationAIStatus,
} from "@warp/shared/constants/app.constants";
import {
  getLocalStorageData,
  setLocalStorageData,
} from "@warp/shared/utils/auth-session.util";
import InfoIcon from "@warp/web/public/images/InfoIcon";
import jwt from "jsonwebtoken";
import { useRouter } from "next/router";
import { FC, useEffect, useMemo, useRef, useState } from "react";
import { FormFieldRender } from "..";
import {
  invitationFormCancelMessage,
  invitationFormSubmitMessage,
  invitationFormValidationFailedMessage,
  prevListingPageredirect,
  prevNextClick,
  raraAlertPopup,
  sendInvitationLoadingStartedMessage,
  sendInvitationValidationFailedMessage,
  warpApprovedSuccessfully,
  warpAssignQuestion,
  warpContentSize,
  warpWarningmessage,
} from "../../../services/platform-window-message.service";
import {
  isAnswered,
  setAnswersData,
  warninglogsave,
} from "../common-functions";
import { useGroupWizardStore } from "../group-wizard.store";
import { useRecommendationListStore } from "../recommendation.store";
import {
  getAnswersByQuestionId,
  getFormFieldStoreState,
  selectFieldOptions,
  showErrorMessage,
  useFormFieldStore,
  useWarningMessageStore,
} from "../store";
import { GroupTabsPropsType, warningmessageObjectType } from "../types";
import QuestionBox from "./QuestionBox";
const isDevelopmentMode = process.env.NODE_ENV === "development";
useWarningMessageStore.getState();
const postParentMessage = (message: string) =>
  window.parent?.postMessage(message, "*");

const useStyles = createStyles((theme) => ({
  activeTab: {
    backgroundColor: "#00216B",
    padding: "12px 16px",
    margin: "-10px -16px",
    height: "33px",
    "&:hover": {
      background: "#00216B",
      border: "0px solid #00216B",
    },
  },
  template2QuestionSuccess: {
    background: theme?.colors?.questionSucces?.[0],
    padding: "12px 16px",
    margin: "-10px -16px",
    height: "33px",
    "&:hover": {
      background: theme?.colors?.questionSucces?.[0],
      borderBottom: "3px solid " + theme.colors.teal[7],
      transition: "0.2s all",
    },
  },
  template2QuestionWarning: {
    background: theme?.colors?.questionWarning?.[0],
    padding: "12px 16px",
    margin: "-10px -16px",
    height: "33px",
    "&:hover": {
      background: theme?.colors?.questionWarning?.[0],
      borderBottom: "3px solid " + theme.colors.orange[5],
      transition: "0.2s all",
    },
  },
  jumpToSub: {
    width: "22px",
    height: "22px",
    borderRadius: "100%",
    background: "rgba(37, 209, 64, 0.7)",
    textAlign: "center",
    lineHeight: "22px",
    color: "#fff",
  },
  jumpToPartiallySub: {
    width: "22px",
    height: "22px",
    borderRadius: "100%",
    background: "#EBEBEB",
    textAlign: "center",
    lineHeight: "22px",
    color: "#B6B6B6",
  },
  jumpToActive: {
    width: "22px",
    height: "22px",
    borderRadius: "100%",
    background: "#FFA93C",
    textAlign: "center",
    lineHeight: "22px",
    color: "#fff",
  },
  noborderbottom: {
    borderBottom: "medium none !important",
  },
  paddingbottomno: {
    paddingBottom: "0 !important",
  },
  paddingtopbotno: {
    paddingBottom: "0 !important",
    paddingTop: "calc(0px / 2) !important",
  },
  breadcrumb: {
    height: "48px",
    backgroundColor: "#162F4B",
    boxShadow: "0px 4px 4px rgba(0, 0, 0, 0.1)",
    borderRadius: "5px",
    marginBottom: "20px",
    padding: "12px 0 12px 20px",
    lineHeight: "24px",
    fontSize: "16px",
  },
  colOrange: {
    color: "#FFA93C",
  },
  colWhite: {
    color: "#fff",
  },
  accordionItem: {
    "&:hover": {
      background: "linear-gradient(91.49deg, #005c81 6.27%, #122f47 93.39%)",
      color: "#fff",
    },
  },
  modalHeader: {
    background: "#EEFCFA",
    height: "68px",
    margin: "-20px -20px 0",
    padding: "10px 20px",
    fontWeight: "bold",
    borderTopLeftRadius: "20px",
    borderTopRightRadius: "20px",
    fontSize: "24px",
  },
  mainModal: {
    borderRadius: "20px",
  },
}));

const tabsStyles = (theme: MantineTheme) => ({
  tab: {
    color:
      theme.colorScheme === "dark"
        ? theme.colors.dark[0]
        : theme.colors.gray[9],
    padding: "10px 16px",
    cursor: "pointer",
    fontSize: theme.fontSizes.xs,
    display: "flex",
    alignItems: "center",

    "&:disabled": {
      opacity: 0.5,
      cursor: "not-allowed",
    },

    "&[data-active]": {
      backgroundColor: "#00216B",
      color: theme.white,
      borderColor: "#00216B",
      border: "none",
      "&:hover": {
        background: "#00216B",
        borderColor: "#00216B",
        border: "none",
      },
    },
  },
});

type Question = {
  id: string;
  content: string;
  FormFields: {
    id: string;
    fieldOptions: { required: boolean };
  }[];
};

const GroupTab: FC<GroupTabsPropsType> = ({
  formField,
  tabs,
  steps,
  hierarchyLevel,
}) => {
  const UpsertValidationWarningLogsMutation =
    useUpsertValidationWarningLogsMutation()[0];
  const getFormFieldsDetail = useGetFormFieldsByQuestionIdLazyQuery()[0];
  const getQuestionList = useGetQuestionListByInvitationIdLazyQuery()[0];
  const getFormfieldRecommendation =
    useGetRecomendationBySubmissionIdAndFormfieldIdLazyQuery()[0];
  const upsertAnswer = useUpsertAnswerMutation()[0];
  const upsertFormInvitationCompetion =
    useUpsertFormInvitationCompetionMutation()[0];
  const updateAnswer = useUpdateAnswerMutation()[0];
  const updateInterimAnswer =
    useUpdateInterimAnswerByQuestionIdAndSubmissionIdMutation()[0];
  const insertInterimAnsweronUpdate = useBulkInsertInterimAnswerMutation()[0];
  const insertBulkAnswer = useBulkInsertAnswerMutation()[0];
  const updateRecommendation =
    useBulkUpdateInterimRecommendationByInterimAnswerIdMutation()[0];
  const removeWarning = useWarningMessageStore(
    (store) => store.removeWarningRuleFields
  );
  const IsPageRefreshed: any | null = getLocalStorageData(
    window.localStorage,
    "IsPageRefreshed"
  );
  const WarningData = useWarningMessageStore.getState().WarningRuleFields;
  let isHierarchyLevel = false;
  if (hierarchyLevel) {
    if (parseInt(hierarchyLevel) === 1) {
      isHierarchyLevel = true;
    }
  }
  if (isHierarchyLevel === false) {
    hierarchyLevel = "7";
  }
  const { query } = useRouter();
  const defaultQuestion = tabs.filter((x) => x.formField.type !== "sub-theme");
  const [getQuestionId, setQuestionId] = useState<string | null>();
  let questionId: any =
    getQuestionId ||
    IsPageRefreshed ||
    defaultQuestion[0].formField?.Question?.id;
  let invitationId: any = query?.invitationId || "";
  const theme = useMantineTheme();
  const { classes } = useStyles();
  const [activeTab, setActiveTab] = useState(questionId);
  // const [file, setFile] = useState<File | null>(null);
  // const [getUploadFile, setUploadFile] = useState(false);
  const nextHandler = useGroupWizardStore((store) => store.nextHandler);
  const prevHandler = useGroupWizardStore((store) => store.prevHandler);
  // const questionHandler = useGroupWizardStore((store) => store.questionHandler);
  const questions = useGroupWizardStore((store) => store.questions);
  const currentWizard = useGroupWizardStore((store) => store.current);
  const [requiredQuestions, setRequiredQuestions] = useState<Question[]>([]);
  //const brsrTemplateDataQuery = useGetGlobalMasterByBrsrTemplateLazyQuery()[0];
  //const { uploadFile } = useFromFileUpload();
  const sectionHandler = useGroupWizardStore((store) => store.sectionHandler);
  const [currentSec, setCurrentSec] = useState<string | null>();
  const [currentBreadCrumb, setCurrentBreadCrumb] = useState<string | null>();
  const [getaccordianBreadCrumb, setAccordianBreadCrumb] = useState<any>([]);
  const chkStore = useFormFieldStore((store) => store);
  const changeColorHandler = useGroupWizardStore(
    (store) => store.changeColorHandler
  );
  const [getInfoIconDetail, setInfoIconDetail] = useState<string | null>();
  //const [opened, { open, close }] = useDisclosure(false);
  const nextRecommHandler = useRecommendationListStore(
    (store) => store.nextRecommHandler
  );
  const prevRecommHandler = useRecommendationListStore(
    (store) => store.prevRecommHandler
  );
  const questionRecommHandler = useGroupWizardStore(
    (store) => store.questionRecommHandler
  );
  const questionrecomm: any = useRecommendationListStore(
    (store) => store.questions
  );
  const questionsRecommForNext: any = useRecommendationListStore((store) =>
    store.hasdata(
      getFormFieldStoreState().formFields.find(
        (m: any) => m.Question?.id === currentWizard?.question
      ),
      true
    )
  );
  const questionsRecommForPrev: any = useRecommendationListStore((store) =>
    store.hasdata(
      getFormFieldStoreState().formFields.find(
        (m: any) => m.Question?.id === currentWizard?.question
      ),
      false
    )
  );

  const userSession = useUserSession();
  const decodedToken: any =
    typeof window !== "undefined"
      ? jwt.decode(String(userSession?.accessToken))
      : null;
  const AITokenValue = !!decodedToken
    ? decodedToken["https://hasura.io/jwt/claims"]["x-hasura-form-with-AI"]
    : null;
  const formAIDetailData: userInvitationAIStatus[] =
    !useFormFieldStore?.getState()?.isCarryForward
      ? !!AITokenValue
        ? AITokenValue?.filter(
            (items: any) =>
              items?.formId == useFormFieldStore?.getState()?.formId &&
              (items?.docWithAI == true || items?.onlyDoc == true)
          )
        : []
      : [];
  const isAutoFilledQuestion = tabs.filter(
    (item) => item.formField.Question?.id === activeTab
  )[0]?.formField?.interfaceOptions?.isAutoFilled;

  const recommendationNewResponce: any = userSession?.GlobalMaster?.filter(
    (x: any) => x.type === "Recommendation_new"
  );

  const raraCompanyId: any = userSession?.GlobalMaster?.filter(
    (x: any) => x.type === "RaraIntegrationAccess"
  );
  const { data: raraCompanies } = useRaraCompanyAccessQuery({
    variables: {
      companyIdList: raraCompanyId[0]?.data[0]?.companyId,
      companyId: userSession?.company?.id,
    },
  });
  // const { data: recommendationNewResponce } = useGetGlobalMasterByTypeQuery({
  //   variables: { type: "Recommendation_new" },
  // });

  let FormHasRecommendation: any = recommendationNewResponce[0]?.data.filter(
    (rec: any) => rec.FormId === formField?.formId
  );

  const formInvitationStatusMutation =
    useUpdateFormInvitationStatusMutation()[0];

  const updateAssesseeUserMappingMutation =
    useUpdateAssesseeUserMappingbyInvitationIdMutation()[0];

  const updateAssesseeUserMappingForResponderMutation =
    useUpdateAssesseeUserMappingForResponderMutation()[0];

  const UpdateValidationWarningLogsMutation =
    useUpdateValidationWarningLogsMutation()[0];
  const updateSuggestionData = useBulkUpdateSuggestionsMutation()[0];
  const WarningMessage = WarningData[0]?.isWarningRule;

  const AutoAppoverRecored: any = userSession?.GlobalMaster?.filter(
    (x: any) => x.type === "InviterFormAutoAppover"
  );

  // const { data: AutoAppoverRecored } =
  //   useGetGlobalMasterDataForInviterFormAutoAppoverQuery();

  // let wheredata: InvitationComment_Bool_Exp = {
  //   invitationId: { _eq: invitationId },
  //   isActive: { _eq: true },
  // };
  // const invitationCommentsListData = useGetinvitationcommentQuery({
  //   variables: {
  //     where: wheredata,
  //   },
  // });
  const { data: invitationAndSubmissionQueryResult } =
    useGetInvitationAndSubmissionDetailsByInvitationIdQuery({
      variables: {
        invitationId,
      },
    });
  const { data: AssesseeUserMappingQueryResult } =
    useGetassesseeuserbyinvitationIdQuery({
      variables: {
        invitationId,
      },
    });

  const getAnswerByQuestionResult =
    useGetAnswerByQuestionIdAndSubmissionIdLazyQuery()[0];

  const InternalCompanyData: any = userSession?.GlobalMaster?.filter(
    (x: any) => x.type === "InternalRequestCompany"
  );

  // const { data: InternalCompanyData } =
  //   useGetGlobalMasterByInternalRequestCompanyQuery();

  const chkCompanyId = InternalCompanyData[0]?.data.filter(
    (a: any) => a.companyId === userSession?.company?.id
  );

  useEffect(() => {
    if (
      IsPageRefreshed &&
      IsPageRefreshed !== "undefined" &&
      IsPageRefreshed !== "null"
    ) {
      setActiveTab(IsPageRefreshed);
      return;
    }
    if (currentWizard?.question) {
      setActiveTab(currentWizard?.question);
      // setLocalStorageData(window.localStorage, "IsPageRefreshed", currentWizard?.question);
      if (userSession?.user?.role === AppRoles.Responder) {
        postParentMessage(
          prevNextClick(currentWizard?.question || "", invitationId)
        );
      }
    }
  }, [currentWizard?.question, invitationId, IsPageRefreshed]);

  useEffect(() => {
    // if (currentSec !== localStorage.getItem("secCurrent")!) {
    //   setCurrentSec(localStorage.getItem("secCurrent")!);
    // }
    if (IsPageRefreshed) {
      // setLoading(true);
      setCurrentSec(
        questions.filter((d: any) => d.questionId === IsPageRefreshed)[0]
          ?.section || currentWizard?.section
      );

      sectionHandler(
        questions.filter((d: any) => d.questionId === IsPageRefreshed)[0]
          ?.section || currentWizard?.section
      );
      // setTimeout((e: any) => {
      //   setLocalStorageData(window.localStorage, "IsPageRefreshed", "null");
      //   setLoading(false);
      // }, 1500);
      return;
    }

    setCurrentSec(
      questions.filter((d: any) => d.questionId === questionId)[0]?.section ||
        currentWizard?.section
    );

    setInfoIconDetail(
      steps.filter(
        (x) =>
          x.key ===
          (questions || defaultQuestion).filter(
            (d: any) => d.questionId === activeTab
          )[0]?.section
      )[0]?.infoIconProps?.content ?? ""
    );
    if (IsPageRefreshed) setActiveTab(activeTab);
  }, [
    currentWizard?.section,
    questionId,
    questions,
    currentSec,
    IsPageRefreshed,
  ]);

  let accordianBreadCrumbData: any = [];

  useEffect(() => {
    if (steps !== undefined) {
      // document
      //   .querySelectorAll("[class$='accordianSubtheme']")
      //   .forEach((item: any) => {
      //     if (item.getAttribute("data-active") === "true") {
      //       //item.click();
      //       item.classList.remove("accordianSubtheme");
      //     }
      //   });

      const getQuestionSubthemeName = tabs
        .filter(
          (p) =>
            p.formField.Question !== null && p.formField.Question !== undefined
        )
        .filter(
          (t) =>
            t.formField?.Section?.id ===
              questions.filter((d: any) => d.questionId === activeTab)[0]
                ?.sectionId && t.formField?.Question?.id === activeTab
        )
        .map((u) => u.formField.subtheme)[0];

      const getBredcrum_tabs = tabs
        .filter(
          (x) =>
            x.formField?.Section?.id ===
              questions.filter((d: any) => d.questionId === activeTab)[0]
                ?.sectionId && x.formField.subtheme === getQuestionSubthemeName
        )
        .filter((y) => y.formField.type === "sub-theme")
        .map((z) => z.formField.interfaceOptions.breadcrumb);
      if (getBredcrum_tabs.length > 0) {
        if (isHierarchyLevel && (getBredcrum_tabs[0] ?? "").indexOf(">") > -1) {
          setCurrentBreadCrumb(getBredcrum_tabs[0]?.split(">")[0]);
        } else {
          setCurrentBreadCrumb(getBredcrum_tabs[0]);
        }
        if (getBredcrum_tabs[0] !== "") {
          if (currentBreadCrumb !== getBredcrum_tabs[0]) {
            if ((getBredcrum_tabs[0] ?? "").indexOf(">") > -1) {
              let collectedItemArray: Array<String> = [];
              getBredcrum_tabs[0]
                ?.split(">")
                .map((item: any, index: number) => {
                  collectedItemArray.push(item.trim().split(" ").join("_"));
                  if (index === 0) {
                    const firstHeading = questions.filter(
                      (d: any) => d.questionId === activeTab
                    )[0]?.section;
                    accordianBreadCrumbData.push(firstHeading);
                  } else if (
                    getBredcrum_tabs[0]?.split(">").length ===
                    index + 1
                  ) {
                    accordianBreadCrumbData.push(collectedItemArray.join("_"));
                    setAccordianBreadCrumb(accordianBreadCrumbData);
                  } else {
                    // accordianBreadCrumbData.push(
                    //   item.trim().split(" ").join("_")
                    // );
                    accordianBreadCrumbData.push(collectedItemArray.join("_"));
                  }

                  // const getDocumentId: any =
                  //   document.querySelector(`[id$="${item.trim()}"]`) || null;
                  // if (getDocumentId !== null) {
                  //   if (getDocumentId.getAttribute("data-active") === null) {
                  //     if (IsPageRefreshed) {
                  //       setTimeout((e: any) => {
                  //         setLocalStorageData(
                  //           window.localStorage,
                  //           "IsPageRefreshed",
                  //           "null"
                  //         );
                  //       }, 2000);
                  //     }
                  //   }
                  // }
                  return null;
                });
            }
          } else if (IsPageRefreshed) {
            if ((getBredcrum_tabs[0] ?? "").indexOf(">") > -1) {
              let collectedItemArray: Array<String> = [];
              getBredcrum_tabs[0]
                ?.split(">")
                .map((item: any, index: number) => {
                  collectedItemArray.push(item.trim().split(" ").join("_"));
                  if (index === 0) {
                    const firstHeading = questions.filter(
                      (d: any) => d.questionId === activeTab
                    )[0]?.section;
                    accordianBreadCrumbData.push(firstHeading);
                  } else if (
                    getBredcrum_tabs[0]?.split(">").length ===
                    index + 1
                  ) {
                    accordianBreadCrumbData.push(collectedItemArray.join("_"));
                    setAccordianBreadCrumb(accordianBreadCrumbData);
                  } else {
                    // accordianBreadCrumbData.push(
                    //   item.trim().split(" ").join("_")
                    // );
                    accordianBreadCrumbData.push(collectedItemArray.join("_"));
                  }
                  // const getDocumentId: any =
                  //   document.querySelector(`[id$="${item.trim()}"]`) || null;
                  // if (getDocumentId !== null) {
                  //   if (getDocumentId.getAttribute("data-active") === null) {
                  //     if (IsPageRefreshed) {
                  //       setTimeout((e: any) => {
                  //         setLocalStorageData(
                  //           window.localStorage,
                  //           "IsPageRefreshed",
                  //           "null"
                  //         );
                  //         getDocumentId.click();
                  //       }, 2000);
                  //     }
                  //   }
                  // }
                  return null;
                });
            }
          }
        }

        return;
      }

      const getBredcrum_steps = steps.filter(
        (x) =>
          x.key ===
          (questions || defaultQuestion).filter(
            (d: any) => d.questionId === activeTab
          )[0]?.section
      )[0]?.breadcrumb;

      if (getBredcrum_steps !== undefined) {
        accordianBreadCrumbData.push(getBredcrum_steps.split(" ").join("_"));
        setAccordianBreadCrumb(accordianBreadCrumbData);
        setCurrentBreadCrumb(getBredcrum_steps);
        if (getBredcrum_steps !== "") {
          if ((getBredcrum_steps ?? "").indexOf(">") > -1) {
            getBredcrum_steps?.split(">").map((item: any, index: number) => {
              accordianBreadCrumbData.push(item.trim().split(" ").join("_"));
              if (getBredcrum_tabs[0]?.split(">").length === index + 1) {
                setAccordianBreadCrumb(accordianBreadCrumbData);
              }

              const getDocumentId: any =
                document.querySelector(`[id*="${item.trim()}"]`) || null;
              if (getDocumentId !== null) {
                if (getDocumentId.getAttribute("data-active") === null) {
                  // getDocumentId.click();
                }
              }
              return null;
            });
          }
        }
        return;
      }
      accordianBreadCrumbData.push(getBredcrum_steps);
      setAccordianBreadCrumb(accordianBreadCrumbData);
      setCurrentBreadCrumb(steps[0].breadcrumb);
    }
  }, [activeTab, currentBreadCrumb, IsPageRefreshed]);

  // console.log("getaccordianBreadCrumb", getaccordianBreadCrumb);

  const InviterAutoAppoverRecord = useMemo(() => {
    if (!AutoAppoverRecored[0]?.data) return null;
    return AutoAppoverRecored[0]?.data.filter((x: any) =>
      userSession?.user?.role === "Invitee"
        ? x.formId === chkStore?.formId &&
          x.companyId ===
            invitationAndSubmissionQueryResult?.FormInvitation[0]
              ?.parentcompanyId &&
          x.IsEnable === true
        : x.formId === chkStore?.formId &&
          x.companyId === userSession?.company?.id &&
          x.UserId.indexOf(userSession?.user?.id) >= 0 &&
          x.IsEnable === true
    );
  }, [
    AutoAppoverRecored,
    chkStore,
    userSession,
    invitationAndSubmissionQueryResult,
  ]);

  const FormSubmissionsStatus = useMemo(() => {
    if (!invitationAndSubmissionQueryResult?.FormInvitation[0]) return null;

    return invitationAndSubmissionQueryResult?.FormInvitation?.filter((m) =>
      userSession?.user?.role === "Consultant"
        ? m.formId === chkStore?.formId
        : m.formId === chkStore?.formId &&
          m.parentcompanyId === userSession?.company?.id
    )[0];
  }, [invitationAndSubmissionQueryResult, chkStore, userSession]);

  let IsApprove = true;
  let Newurl = window.location.href;
  if (Newurl.includes("viewrecommendation")) {
    IsApprove = false;
  } else {
    IsApprove = true;
  }

  const isDelegateQuestion = useMemo(() => {
    if (!invitationAndSubmissionQueryResult?.FormInvitation[0]) return false;
    let getIsDelegate = invitationAndSubmissionQueryResult?.FormInvitation[0];
    if (
      getIsDelegate?.Form?.isDelegateQuestion &&
      (query?.mode === FormMode.Start ||
        query?.mode === FormMode.ViewRecommendation)
    )
      return true;
    else return false;
  }, [invitationAndSubmissionQueryResult, query?.mode]);

  const AssesseeUserMappingResult: any = useMemo(() => {
    if (!AssesseeUserMappingQueryResult?.AssesseeUserMapping[0]) return null;
    return AssesseeUserMappingQueryResult?.AssesseeUserMapping;
  }, [AssesseeUserMappingQueryResult]);

  let isAlreadyAssigned: any =
    AssesseeUserMappingResult?.filter((x: any) => x.questionId === activeTab)
      .length > 0
      ? true
      : false;

  setLocalStorageData(
    window.localStorage,
    "isResponderUser",
    AssesseeUserMappingResult?.filter(
      (x: any) => x.userId === userSession?.user?.id
    ).length > 0
      ? true
      : false
  );

  //const getCompanyDetailById = useGetCompanyDetailByIdLazyQuery()[0];

  // console.log({ previousTab, activeTab });
  //const getInterimRecommendation = useGetRecommendationByInterimAnswerIdAndQuestionIdLazyQuery()[0];
  const [loading, setLoading] = useState(false);

  const { refetch: getformFieldsbySubmissionId } =
    useGetformFieldsbySubmissionIdQuery({ skip: true });

  const getQuestionRecord = async (invitationId: string) => {
    const questionList = await getQuestionList({
      variables: { invitationId },
    });

    const filteredQuestions =
      questionList?.data?.FormInvitation[0]?.Form?.Sections.flatMap((section) =>
        section.Questions.filter((question) =>
          question.FormFields.some((field) => field.fieldOptions.required)
        )
      );
    setRequiredQuestions(filteredQuestions || []);
  };

  useEffect(() => {
    if (invitationId) {
      getQuestionRecord(invitationId);
    }
  }, [invitationId]);

  // Use memoization for filtering questions based on the required ones
  // const finalQuestionResult = useMemo(() => {
  //   return questions.filter((item) =>
  //     requiredQuestions.some((record) => record.id === item.questionId)
  //   );
  // }, [questions, requiredQuestions]);

  // steps.map((stepData) => {
  //   const step = stepData?.component?.props?.formField;
  //   step?.children.map((tabdata: any) => {
  //     tabdata?.children.map((formFieldData: any) => {
  //       let tabColor = isAnswered(formFieldData, false, chkStore);
  //       changeColorHandler(tabColor, formFieldData?.Question?.id);
  //     });
  //   });
  // });

  // let allquestiondata = useGroupWizardStore.getState().questions;

  // const submittedQuestiondata = useMemo(() => {
  //   return allquestiondata.filter((item) =>
  //     requiredQuestions.some((record) => record.id === item.questionId)
  //   );
  // }, [allquestiondata, requiredQuestions]);

  const getProgressBarPercentageForListing = () => {
    steps.map((stepData) => {
      const step = stepData?.component?.props?.formField;
      step?.children.map((tabdata: any) => {
        tabdata?.children.map((formFieldData: any) => {
          let tabColor = isAnswered(formFieldData, false, chkStore);
          changeColorHandler(tabColor, formFieldData?.Question?.id);
        });
      });
    });
    let allquestiondata = useGroupWizardStore
      .getState()
      .questions.filter((item) =>
        requiredQuestions.some((record) => record.id === item.questionId)
      );

    return (
      (allquestiondata.filter((item: any) => item.color === true).length /
        allquestiondata.length) *
      100
    ).toFixed(0);
  };

  const insertRaraValidationAndRating =
    useInsertRaraValidationAndRatingMutation()[0];

  //Warning Rules changes started

  //Warning Rules changes end
  const assesseeUserLength =
    AssesseeUserMappingQueryResult?.AssesseeUserMapping.filter(
      (user: any) =>
        user.userId === userSession?.user?.id && user.questionId === questionId
    ) || [];

  const upsertQuestionAnswer = async (formFieldId: string) => {
    //Warning Rules changes started
    if (loading) return false;
    setLoading(true);

    const _formField = getFormFieldStoreState().formFields.find(
      (m: any) => m.Question?.id === formFieldId
    );
    const questionId = _formField?.Question?.id;
    const submissionId = getFormFieldStoreState().formSubmissionId;

    if (!submissionId) {
      //alert("Invalid submission id");
      setLoading(false);
      return;
    }

    if (!questionId) {
      //alert("Invalid question id");
      setLoading(false);
      return;
    }

    const answers = getAnswersByQuestionId(questionId);
    // No answer found
    if (!answers?.length) {
      //setLoading(false);
      setTimeout(() => {
        setLoading(false);
      }, 2000);
      return;
    }
    const { data, error } = await getAnswerByQuestionResult({
      variables: {
        questionId: questionId,
        submissionId: submissionId,
      },
    });
    let Isvalidate = true;
    Isvalidate =
      useGroupWizardStore
        .getState()
        .questions.filter((item: any) => item.color === false).length > 0
        ? false
        : true;
    if (isAlreadyAssigned) {
      let latestDataArray: any = [];
      let ResponderDataArray: any = [];
      let _chkIsAnswered = false;
      answers?.map((item: any) => {
        if (item.value !== "" || item?.value?.length > 0) {
          _chkIsAnswered = true;
        }
        let latestValue = data?.Answer?.filter(
          (x) =>
            x.questionId === item.questionId &&
            x.formFieldId === item.formFieldId
        )[0]?.data;
        if (
          item.value === "" ||
          item?.value?.length === 0 ||
          item?.value === undefined
        ) {
          latestDataArray.push({
            submissionId: item.submissionId,
            questionId: item.questionId,
            formFieldId: item.formFieldId,
            value: item?.value,
          });
        } else {
          latestDataArray.push({
            submissionId: item.submissionId,
            questionId: item.questionId,
            formFieldId: item.formFieldId,
            value: item?.value,
          });
        }
      });
      await setAnswersData(
        data,
        submissionId,
        answers,
        latestDataArray,
        questionId,
        userSession?.user?.id,
        useFormFieldStore?.getState()?.answer,
        String(chkStore?.formId),
        invitationId,
        "GroupTab",
        "",
        "",
        getFormFieldsDetail,
        getFormfieldRecommendation,
        upsertAnswer,
        updateAnswer,
        updateInterimAnswer,
        insertInterimAnsweronUpdate,
        insertBulkAnswer,
        updateRecommendation,
        updateSuggestionData,
        useFormFieldStore?.getState()?.Suggestions
      );
      if (userSession?.user?.role !== "Responder") {
        await upsertFormInvitationCompetion({
          variables: {
            completion: getProgressBarPercentageForListing(),
            invitationId: invitationId,
          },
        });
      }
      const questiondata = tabs.filter(
        (item) => item.formField.Question?.id === questionId
      )[0]?.formField;
      const partiallyanswereddata: any | null = getLocalStorageData(
        window.localStorage,
        "IsPartiallyanswered"
      );
      let partially_ansquestion = JSON.parse(partiallyanswereddata)[0]
        ?.questionid;
      let partially_ansstatus = JSON.parse(partiallyanswereddata)[0]?.status;
      console.log(
        "IsPartiallyanswered",
        JSON.parse(partiallyanswereddata),
        partially_ansquestion,
        partially_ansstatus
      );
      const status =
        partially_ansstatus === false
          ? QuestionStatus.Responded
          : QuestionStatus.Pending;
      if (partially_ansstatus && partially_ansquestion === activeTab) {
        if (formField.fieldOptions.required == true) {
          if (
            userSession?.user?.role === "Responder" &&
            assesseeUserLength.length > 0
          ) {
            await Promise.all([
              latestDataArray?.map(async (a: any) => {
                const resultFormInvitation: any =
                  await updateAssesseeUserMappingForResponderMutation({
                    variables: {
                      invitationId: invitationId,
                      invitationStatus: status,
                      userId: userSession?.user?.id,
                      questionId: questionId,
                      isResponder: true,
                    },
                  });
              }),
            ]);
          } else if (
            userSession?.user?.role === "Invitee" &&
            assesseeUserLength.length > 0
          ) {
            await Promise.all([
              latestDataArray?.map(async (a: any) => {
                const resultFormInvitation: any =
                  await updateAssesseeUserMappingForResponderMutation({
                    variables: {
                      invitationId: invitationId,
                      invitationStatus: status,
                      userId: userSession?.user?.id,
                      questionId: questionId,
                      isResponder: true,
                    },
                  });
              }),
            ]);
          } else {
            await Promise.all(
              latestDataArray?.map(async (a: any) => {
                const resultFormInvitation: any =
                  await updateAssesseeUserMappingMutation({
                    variables: {
                      invitationId: invitationId,
                      invitationStatus: status,
                      userId: userSession?.user?.id,
                      questionId: questionId,
                    },
                  });
              })
            );
          }
        } else if (formField.fieldOptions.required == false) {
          if (
            userSession?.user?.role === "Responder" &&
            assesseeUserLength.length > 0
          ) {
            await Promise.all(
              latestDataArray?.map(async (a: any) => {
                const resultFormInvitation: any =
                  await updateAssesseeUserMappingForResponderMutation({
                    variables: {
                      invitationId: invitationId,
                      invitationStatus: status,
                      userId: userSession?.user?.id,
                      questionId: questionId,
                      isResponder: true,
                    },
                  });
              })
            );
          } else if (
            userSession?.user?.role === "Invitee" &&
            assesseeUserLength.length > 0
          ) {
            await Promise.all(
              latestDataArray?.map(async (a: any) => {
                const resultFormInvitation: any =
                  await updateAssesseeUserMappingForResponderMutation({
                    variables: {
                      invitationId: invitationId,
                      invitationStatus: status,
                      userId: userSession?.user?.id,
                      questionId: questionId,
                      isResponder: true,
                    },
                  });
              })
            );
          } else {
            await Promise.all(
              latestDataArray?.map(async (a: any) => {
                const resultFormInvitation: any =
                  await updateAssesseeUserMappingMutation({
                    variables: {
                      invitationId: invitationId,
                      invitationStatus: status,
                      userId: userSession?.user?.id,
                      questionId: questionId,
                    },
                  });
              })
            );
          }
        }
      } else {
        if (
          userSession?.user?.role === "Responder" &&
          assesseeUserLength.length > 0
        ) {
          await Promise.all(
            latestDataArray?.map(async (a: any) => {
              const resultFormInvitation: any =
                await updateAssesseeUserMappingForResponderMutation({
                  variables: {
                    invitationId: invitationId,
                    invitationStatus: status,
                    userId: userSession?.user?.id,
                    questionId: questionId,
                    isResponder: true,
                  },
                });
            })
          );
        } else if (
          userSession?.user?.role === "Invitee" &&
          assesseeUserLength.length > 0
        ) {
          await Promise.all(
            latestDataArray?.map(async (a: any) => {
              const resultFormInvitation: any =
                await updateAssesseeUserMappingForResponderMutation({
                  variables: {
                    invitationId: invitationId,
                    invitationStatus: status,
                    userId: userSession?.user?.id,
                    questionId: questionId,
                    isResponder: true,
                  },
                });
            })
          );
        } else {
          await Promise.all(
            latestDataArray?.map(async (a: any) => {
              const resultFormInvitation: any =
                await updateAssesseeUserMappingMutation({
                  variables: {
                    invitationId: invitationId,
                    invitationStatus: status,
                    userId: userSession?.user?.id,
                    questionId: questionId,
                  },
                });
            })
          );
        }
      }
    } else {
      await setAnswersData(
        data,
        submissionId,
        answers,
        answers,
        questionId,
        userSession?.user?.id,
        useFormFieldStore?.getState()?.answer,
        String(chkStore?.formId),
        invitationId,
        "GroupTab",
        "",
        "",
        getFormFieldsDetail,
        getFormfieldRecommendation,
        upsertAnswer,
        updateAnswer,
        updateInterimAnswer,
        insertInterimAnsweronUpdate,
        insertBulkAnswer,
        updateRecommendation,
        updateSuggestionData,
        useFormFieldStore?.getState()?.Suggestions
      );
      await upsertFormInvitationCompetion({
        variables: {
          completion: getProgressBarPercentageForListing(),
          invitationId: invitationId,
        },
      });
    }
    // console.log({ result });
    setLoading(false);
    return true;
  };
  const SubmitDetails = async (
    activeTab: any,
    //upsertAnswer: any,
    setLoading: any,
    loading: any
  ) => {
    postParentMessage(sendInvitationLoadingStartedMessage());

    // const isJson = (str: any) => {
    //   try {
    //     JSON.parse(str);
    //   } catch (e) {
    //     return false;
    //   }
    //   return true;
    // };
    // const isRound = (str: any) => {
    //   if (str === undefined) return str;
    //   const strNumber: any = Number(str.toString().replace("%", ""));
    //   const parsed = parseInt(strNumber);
    //   if (isNaN(parsed)) {
    //     const lower_str = String(str).toLocaleLowerCase();
    //     const final_str =
    //       lower_str === "yes"
    //         ? lower_str.replace(lower_str, "Yes")
    //         : lower_str === "no"
    //         ? lower_str.replace(lower_str, "No")
    //         : lower_str === "na"
    //         ? str.replace(lower_str, "NA")
    //         : str;
    //     return final_str;
    //   }
    //   //const strPercentage = indexOf(str, "%") > -1 ? "%" : "";
    //   const dataDecimal =
    //     indexOf(str, ".") > -1
    //       ? parseFloat(str.toString().replace("%", "")).toFixed(2)
    //       : Math.round(str.toString().replace("%", ""));
    //   return dataDecimal;
    // };
    // const chunkSubstr = (str: string, size: number) => {
    //   const numChunks = Math.ceil(str.length / size);
    //   const chunks = new Array(numChunks);
    //   //console.log("str", str);
    //   for (let i = 0, o = 0; i < numChunks; ++i, o += size) {
    //     chunks[i] = str.substr(o, size);
    //   }
    //   //console.log("str", chunks);
    //   return chunks;
    // };
    // const createPdfFileOnS3 = async (invitationId: any, formId: any) => {
    //   try {
    //     const { data, error } = await getCompanyDetailById({
    //       variables: {
    //         id: userSession?.company?.id,
    //       },
    //     });

    //     if (!formId || !invitationId) return;
    //     const brsrTemplateData = await brsrTemplateDataQuery();

    //     if (
    //       formId === brsrTemplateData?.data?.GlobalMaster[0]?.data["formId"]
    //     ) {
    //       const StoreAnswer: any = useFormFieldStore.getState().answer;
    //       let templateHtml: any =
    //         brsrTemplateData?.data?.GlobalMaster[0]?.data["template"];
    //       // let templateHtml = await fetch(
    //       //   brsrTemplateData?.GlobalMaster[0]?.data["template_link"]
    //       // ).then((r) => {
    //       //   return r.text().then((d) => d);
    //       // });
    //       const procesing_fields: any =
    //         brsrTemplateData?.data?.GlobalMaster[0]?.data["procesing_fields"];
    //       procesing_fields.map(async (detail: any) => {
    //         switch (detail.type) {
    //           case "free_text": {
    //             try {
    //               let value = jsonata(detail.rule).evaluate(StoreAnswer);
    //               value = value === undefined ? "NA" : value;
    //               if (detail.position === "@s1_q9_a") {
    //                 templateHtml = templateHtml.replaceAll(
    //                   detail.position,
    //                   value
    //                 );
    //                 return;
    //               } else if (detail.position === "@s1_q9_b") {
    //                 templateHtml = templateHtml.replaceAll(
    //                   detail.position,
    //                   value
    //                 );
    //                 return;
    //               } else if (detail.position === "@hyphen") {
    //                 templateHtml = templateHtml.replaceAll(
    //                   detail.position,
    //                   value
    //                 );
    //                 return;
    //               }
    //               templateHtml = templateHtml.replace(detail.position, value);
    //               return;
    //             } catch (error) {
    //               templateHtml = templateHtml.replace(detail.position, "");
    //             }
    //           }
    //           case "table": {
    //             try {
    //               let tableValue: any = jsonata(detail.rule).evaluate(
    //                 StoreAnswer
    //               );
    //               let typeDet = typeof tableValue;
    //               let tableValueData: any = [];
    //               if (tableValue !== undefined) {
    //                 if (typeDet === "string") {
    //                   tableValueData.push(tableValue);
    //                 } else {
    //                   tableValueData = tableValue;
    //                 }
    //               }
    //               let tableRows = "";
    //               tableValueData.map((ruleDetail: any) => {
    //                 let tableTdsDetail = ruleDetail.split("~");
    //                 let tableTd = "";
    //                 let allEqual = false;
    //                 let isAllYesNo = false;
    //                 let checkAllYesNo = [...tableTdsDetail];
    //                 //checkAllYesNo.shift();
    //                 if (checkAllYesNo.length > 0) {
    //                   allEqual =
    //                     checkAllYesNo.every((v: any) => v === "yes") ||
    //                     checkAllYesNo.every((v: any) => v === "no");
    //                 }
    //                 if (allEqual) {
    //                   isAllYesNo = true;
    //                   tableTd =
    //                     tableTd +
    //                     "<td colspan='" +
    //                     checkAllYesNo.length +
    //                     "'> <div class='centeraligntxt'>" +
    //                     isRound(checkAllYesNo[0]) +
    //                     "</div></td>";
    //                 }
    //                 if (!isAllYesNo) {
    //                   tableTdsDetail.map((tdDetail: any) => {
    //                     let stringValue = "";
    //                     if (isJson(tdDetail))
    //                       stringValue =
    //                         JSON.parse(tdDetail) === null
    //                           ? ""
    //                           : JSON.parse(tdDetail).toString();
    //                     else stringValue = tdDetail;
    //                     tableTd =
    //                       tableTd +
    //                       "<td class='empwrktbltd'>" +
    //                       isRound(stringValue) +
    //                       "</td>";
    //                   });
    //                 }
    //                 tableRows = tableRows + "<tr>" + tableTd + "</tr>";
    //               });
    //               templateHtml = templateHtml.replace(
    //                 detail.position,
    //                 tableRows
    //               );
    //               return;
    //             } catch (error) {
    //               templateHtml = templateHtml.replace(detail.position, "");
    //             }
    //           }
    //           case "list_index": {
    //             try {
    //               let ulValue = jsonata(detail.rule).evaluate(StoreAnswer);
    //               let typeUlDet = typeof ulValue;
    //               let ulData: any = [];
    //               if (typeUlDet === "string") {
    //                 ulData.push(ulValue);
    //               } else {
    //                 ulData = ulValue;
    //               }
    //               let ulhtml = "";
    //               ulData.map((ulDetail: any) => {
    //                 let lihtml = "";
    //                 let listData = ulDetail.split("~");

    //                 let allEqual = false;
    //                 let isAllYesNo = false;
    //                 let checkAllYesNo = [...listData];
    //                 //checkAllYesNo.shift();
    //                 if (checkAllYesNo.length > 0) {
    //                   allEqual = checkAllYesNo.every(
    //                     (v: any) => v === checkAllYesNo[0]
    //                   );
    //                 }
    //                 if (allEqual) {
    //                   isAllYesNo = true;
    //                   ulhtml =
    //                     ulhtml +
    //                     "<div class='centeraligntxt'>" +
    //                     isRound(listData[0]) +
    //                     "</div>";
    //                 }
    //                 if (!isAllYesNo) {
    //                   listData.map((liDetail: any, index: any) => {
    //                     lihtml =
    //                       lihtml +
    //                       "<li>Principle " +
    //                       parseInt(index + 1) +
    //                       ":" +
    //                       liDetail +
    //                       "</li>";
    //                   });
    //                   ulhtml =
    //                     ulhtml +
    //                     "<ul style='list-style-type: disc'>" +
    //                     lihtml +
    //                     "</ul>";
    //                 }
    //               });
    //               templateHtml = templateHtml.replace(detail.position, ulhtml);
    //               return;
    //             } catch (error) {
    //               templateHtml = templateHtml.replace(detail.position, "");
    //             }
    //           }
    //           case "paragraph": {
    //             try {
    //               let paraValue = jsonata(detail.rule).evaluate(StoreAnswer);
    //               let typeUlDet = typeof paraValue;
    //               let ulData: any = [];
    //               if (typeUlDet === "string") {
    //                 ulData.push(paraValue);
    //               } else {
    //                 ulData = paraValue;
    //               }
    //               let phtml = "";
    //               ulData.map((ulDetail: any) => {
    //                 let listData = ulDetail.split("~");
    //                 listData.map((pDetail: any) => {
    //                   phtml =
    //                     phtml + "<p class='generaltxt'>" + pDetail + "</p>";
    //                 });
    //               });
    //               templateHtml = templateHtml.replace(detail.position, phtml);
    //               return;
    //             } catch (error) {
    //               templateHtml = templateHtml.replace(detail.position, "");
    //             }
    //           }
    //           default:
    //             break;
    //         }
    //       });
    //       Font.register({
    //         family: "OpenSans",
    //         fonts: [
    //           {
    //             src: brsrTemplateData?.data?.GlobalMaster[0]?.data[
    //               "font_normal"
    //             ],
    //           },
    //           {
    //             src: brsrTemplateData?.data?.GlobalMaster[0]?.data["font_bold"],
    //             fontWeight: "bold",
    //           },
    //           {
    //             src: brsrTemplateData?.data?.GlobalMaster[0]?.data[
    //               "font_normal_italic"
    //             ],
    //             fontWeight: "normal",
    //             fontStyle: "italic",
    //           },
    //         ],
    //       });
    //       Font.registerHyphenationCallback((word, glyphString) => {
    //         const middle = Math.floor(word.length / 2);

    //         const parts =
    //           word.length === 1
    //             ? [word]
    //             : word.length > 16
    //             ? chunkSubstr(word, 8)
    //             : [word.substr(0, middle), word.substr(middle)];

    //         // Check console to see words parts
    //         //console.log(word, parts);
    //         return parts;
    //       });
    //       const blob = await pdf(
    //         <Document>
    //           <Page size="A4" wrap={false}>
    //             <PDFText
    //               style={{
    //                 fontSize: 12,
    //                 color: "#868e96",
    //                 marginTop: 20,
    //                 marginBottom: 20,
    //                 marginLeft: 20,
    //                 textTransform: "capitalize",
    //               }}
    //               fixed
    //             >
    //               {data?.Company[0]?.name} {"\n"}
    //               Business Responsibility and Sustainability Reporting
    //             </PDFText>
    //             <Html>{templateHtml}</Html>
    //           </Page>
    //         </Document>
    //       ).toBlob();
    //       //saveAs(blob, "BRSR_TEST");
    //       let fileName = invitationId + ".pdf";
    //       const file = new File([blob], fileName, {
    //         type: blob.type,
    //         lastModified: Date.now(),
    //       });
    //       const fileResult = await uploadFile(file, true, "");
    //       // console.log("onCreatePdfFileOnS3 - Success", fileResult);
    //     }
    //   } catch (error) {
    //     console.log("onCreatePdfFileOnS3 - Error", error);
    //   }
    // };

    // const upsertAnswer = useUpsertAnswerMutation()[0];
    // const [loading, setLoading] = useState(false);
    const formData = useFormFieldStore.getState();
    // const answer = formData.answer;
    // let getrootfield = prepareFormForRender(formData.formFields);
    let Isvalidate = true;
    //let IsvalidateArray: any = [];
    // createPdfFileOnS3(
    //   "5d42dc9c-568f-4e76-bec0-17414161fef4",
    //   "6331b3f1-d49f-48f6-9bbe-bfe331506dcf"
    // );
    const upsertQuestionAnswer = async (formFieldId: string) => {
      if (loading) return false;
      setLoading(false);

      const _formField = getFormFieldStoreState().formFields.find(
        (m: any) => m.Question?.id === formFieldId
      );
      const questionId = _formField?.Question?.id;
      const submissionId = getFormFieldStoreState().formSubmissionId;

      if (!submissionId) {
        //alert("Invalid submission id");
        setLoading(false);
        return;
      }

      if (!questionId) {
        //alert("Invalid question id");
        setLoading(false);
        return;
      }

      const answers = getAnswersByQuestionId(questionId);
      //console.log({ answers });

      // No answer found
      if (!answers?.length) {
        setLoading(false);
        return;
      }
      const { data, error } = await getAnswerByQuestionResult({
        variables: {
          questionId: questionId,
          submissionId: submissionId,
        },
      });
      Isvalidate =
        useGroupWizardStore
          .getState()
          .questions.filter((item: any) => item.color === false).length > 0
          ? false
          : true;

      if (isAlreadyAssigned) {
        let latestDataArray: any = [];
        let _chkIsAnswered = false;
        let answerDataArray: any = [];
        answers?.map((item: any) => {
          if (
            ((item.value !== undefined && item.value !== "") ||
              item?.value?.length > 0) &&
            Isvalidate
          ) {
            _chkIsAnswered = true;
          }
          let latestValue = data?.Answer?.filter(
            (x: any) =>
              x.questionId === item.questionId &&
              x.formFieldId === item.formFieldId
          )[0]?.data;
          if (
            (item.value === "" ||
              item?.value?.length === 0 ||
              item?.value === undefined) &&
            latestValue?.value !== ""
          ) {
            latestDataArray.push({
              submissionId: item.submissionId,
              questionId: item.questionId,
              formFieldId: item.formFieldId,
              value: latestValue?.value,
            });
            answerDataArray.push({
              submissionId: item.submissionId,
              questionId: item.questionId,
              formFieldId: item.formFieldId,
              oldValue: latestValue?.value,
              newValue: item.value,
            });
          } else {
            latestDataArray.push({
              submissionId: item.submissionId,
              questionId: item.questionId,
              formFieldId: item.formFieldId,
              value: item?.value,
            });
            answerDataArray.push({
              submissionId: item.submissionId,
              questionId: item.questionId,
              formFieldId: item.formFieldId,
              oldValue: latestValue?.value,
              newValue: item.value,
            });
          }
        });
        await setAnswersData(
          data,
          submissionId,
          answers,
          latestDataArray,
          questionId,
          userSession?.user?.id,
          useFormFieldStore?.getState()?.answer,
          String(chkStore?.formId),
          invitationId,
          "GroupTab",
          "",
          "",
          getFormFieldsDetail,
          getFormfieldRecommendation,
          upsertAnswer,
          updateAnswer,
          updateInterimAnswer,
          insertInterimAnsweronUpdate,
          insertBulkAnswer,
          updateRecommendation,
          updateSuggestionData,
          useFormFieldStore?.getState()?.Suggestions
        );
        if (userSession?.user?.role !== "Responder") {
          await upsertFormInvitationCompetion({
            variables: {
              completion: getProgressBarPercentageForListing(),
              invitationId: invitationId,
            },
          });
        }
        if (_chkIsAnswered) {
          if (
            userSession?.user?.role === "Responder" &&
            assesseeUserLength.length > 0
          ) {
            const resultFormInvitation: any =
              await updateAssesseeUserMappingForResponderMutation({
                variables: {
                  invitationId: invitationId,
                  invitationStatus: QuestionStatus.Responded,
                  userId: userSession?.user?.id,
                  questionId: questionId,
                  isResponder: true,
                },
              });
          } else if (
            userSession?.user?.role === "Invitee" &&
            assesseeUserLength.length > 0
          ) {
            const resultFormInvitation: any =
              await updateAssesseeUserMappingForResponderMutation({
                variables: {
                  invitationId: invitationId,
                  invitationStatus: QuestionStatus.Responded,
                  userId: userSession?.user?.id,
                  questionId: questionId,
                  isResponder: true,
                },
              });
          } else {
            const resultFormInvitation: any =
              await updateAssesseeUserMappingMutation({
                variables: {
                  invitationId: invitationId,
                  invitationStatus: QuestionStatus.Responded,
                  userId: userSession?.user?.id,
                  questionId: questionId,
                },
              });
          }
        } else {
          answerDataArray?.map((itemAnswer: any) => {
            if (itemAnswer.oldValue !== "" && itemAnswer.newValue === "") {
              if (
                userSession?.user?.role === "Responder" &&
                assesseeUserLength.length > 0
              ) {
                const resultFormInvitation: any =
                  updateAssesseeUserMappingForResponderMutation({
                    variables: {
                      invitationId: invitationId,
                      invitationStatus: QuestionStatus.Pending,
                      userId: userSession?.user?.id,
                      questionId: questionId,
                      isResponder: true,
                    },
                  });
              } else if (
                userSession?.user?.role === "Invitee" &&
                assesseeUserLength.length > 0
              ) {
                const resultFormInvitation: any =
                  updateAssesseeUserMappingForResponderMutation({
                    variables: {
                      invitationId: invitationId,
                      invitationStatus: QuestionStatus.Pending,
                      userId: userSession?.user?.id,
                      questionId: questionId,
                      isResponder: true,
                    },
                  });
              } else {
                const resultFormInvitation: any =
                  updateAssesseeUserMappingMutation({
                    variables: {
                      invitationId: invitationId,
                      invitationStatus: QuestionStatus.Pending,
                      userId: userSession?.user?.id,
                      questionId: questionId,
                    },
                  });
              }
            } else if (
              (itemAnswer.oldValue === "" && itemAnswer.newValue === "") ||
              (itemAnswer.oldValue === undefined &&
                itemAnswer.newValue === undefined)
            ) {
              if (
                userSession?.user?.role === "Responder" &&
                assesseeUserLength.length > 0
              ) {
                const resultFormInvitation: any =
                  updateAssesseeUserMappingForResponderMutation({
                    variables: {
                      invitationId: invitationId,
                      invitationStatus: QuestionStatus.Pending,
                      userId: userSession?.user?.id,
                      questionId: questionId,
                      isResponder: true,
                    },
                  });
              } else if (
                userSession?.user?.role === "Invitee" &&
                assesseeUserLength.length > 0
              ) {
                const resultFormInvitation: any =
                  updateAssesseeUserMappingForResponderMutation({
                    variables: {
                      invitationId: invitationId,
                      invitationStatus: QuestionStatus.Pending,
                      userId: userSession?.user?.id,
                      questionId: questionId,
                      isResponder: true,
                    },
                  });
              } else {
                const resultFormInvitation: any =
                  updateAssesseeUserMappingMutation({
                    variables: {
                      invitationId: invitationId,
                      invitationStatus: QuestionStatus.Pending,
                      userId: userSession?.user?.id,
                      questionId: questionId,
                    },
                  });
              }
            }
          });
        }
      } else {
        await setAnswersData(
          data,
          submissionId,
          answers,
          answers,
          questionId,
          userSession?.user?.id,
          useFormFieldStore?.getState()?.answer,
          String(chkStore?.formId),
          invitationId,
          "GroupTab",
          "",
          "",
          getFormFieldsDetail,
          getFormfieldRecommendation,
          upsertAnswer,
          updateAnswer,
          updateInterimAnswer,
          insertInterimAnsweronUpdate,
          insertBulkAnswer,
          updateRecommendation,
          updateSuggestionData,
          useFormFieldStore?.getState()?.Suggestions
        );
        await upsertFormInvitationCompetion({
          variables: {
            completion: getProgressBarPercentageForListing(),
            invitationId: invitationId,
          },
        });
      }
      // console.log({ result });
      setLoading(false);
      return true;
    };

    formData.formFields.map(async (item: any) => {
      const UpdateFiledOption = selectFieldOptions(formData, item.id);
    });

    await upsertQuestionAnswer(activeTab);

    checkQuestionAnswers(true);

    Isvalidate =
      useGroupWizardStore
        .getState()
        .questions.filter((item: any) => item.color === false).length > 0
        ? false
        : true;
    console.log({
      Isvalidate: Isvalidate,
      activeTab: activeTab,
      formData: formData,
      questions: useGroupWizardStore.getState().questions,
    });
    if (Isvalidate === true) {
      setLoading(false);
      const {
        formId,
        formInvitationId: invitationId,
        formSubmissionId: submissionId,
      } = getFormFieldStoreState();

      const isResponderUsercheck: any | null = getLocalStorageData(
        window.localStorage,
        "isResponderUser"
      );

      if (isResponderUsercheck === "true") {
        // const emailSendResponse = await fetch(
        //   "/api/question-assign-email-invitation",
        //   {
        //     method: "POST",
        //     headers: {
        //       "content-type": "application/json",
        //     },
        //     body: JSON.stringify({
        //       id: invitationId,
        //       type: "QuestionResponse",
        //       companyId: userSession?.company?.id,
        //       formId: chkStore?.formId,
        //       userId: AssesseeUserMappingResult?.filter(
        //         (x: any) => x.userId === userSession?.user?.id
        //       )[0]?.reviewerUserId,
        //     }),
        //   }
        // );
        if (isAlreadyAssigned) {
          const answers = getAnswersByQuestionId(questionId);
          if (
            answers.filter((x: any) => x.value === "" || x.value === undefined)
              .length === answers.length
          ) {
            const resultFormInvitation: any =
              await updateAssesseeUserMappingMutation({
                variables: {
                  invitationId: invitationId,
                  invitationStatus: QuestionStatus.Pending,
                  userId: userSession?.user?.id,
                  questionId: activeTab,
                },
              });
          } else {
            const resultFormInvitation: any =
              await updateAssesseeUserMappingMutation({
                variables: {
                  invitationId: invitationId,
                  invitationStatus: QuestionStatus.Responded,
                  userId: userSession?.user?.id,
                  questionId: activeTab,
                },
              });
          }
        }
        postParentMessage(sendInvitationValidationFailedMessage());
        postParentMessage(invitationFormSubmitMessage());
      } else {
        const result = await fetch("/api/submit-form", {
          method: "POST",
          body: JSON.stringify({
            submissionId,
          }),
          headers: {
            "Content-Type": "application/json",
            Authorization: userSession?.accessToken ?? "",
          },
        }).then((res) => res.json());
        await fetch("/api/calculate-score/form-submission-email", {
          method: "POST",
          body: JSON.stringify({
            formId,
            submissionId,
            invitationId,
            companyId: userSession?.company?.id,
          }),
          headers: {
            "Content-Type": "application/json",
            Authorization: userSession?.accessToken ?? "",
          },
        });
        /*await fetch("/api/calculate-score", {
                method: "POST",
                body: JSON.stringify({
                formId,
                submissionId,
                invitationId,
                }),
                headers: {
                "Content-Type": "application/json",
                Authorization: userSession?.accessToken ?? "",
                },
                });
                await fetch("/api/calculate-score/form-submission-email", {
                method: "POST",
                body: JSON.stringify({
                formId,
                submissionId,
                invitationId,
                companyId: userSession?.company?.id,
                }),
                headers: {
                "Content-Type": "application/json",
                Authorization: userSession?.accessToken ?? "",
                },
                });
                
                await fetch("/api/progress-report-score", {
                method: "POST",
                body: JSON.stringify({
                formId,
                submissionId,
                invitationId,
                isApproved: "true",
                }),
                headers: {
                "Content-Type": "application/json",
                Authorization: userSession?.accessToken ?? "",
                },
                });*/

        setLoading(false);

        let companyId: any = raraCompanies?.ParentCompanyMapping?.filter(
          (rec: any) => rec.CompanyId === userSession?.company?.id
        )[0]?.CompanyId;

        // if (companyId && submissionId && invitationId) {
        //   await processDocumentRating(submissionId, invitationId);
        // }

        // if (formId === "1cca8240-d2a1-424c-9cc2-36e44c8ef139") {
        //   await createPdfFileOnS3(invitationId, formId);
        // }
        postParentMessage(sendInvitationValidationFailedMessage());
        postParentMessage(invitationFormSubmitMessage());
        // router.push("/invitation/list");
      }
    } else {
      showErrorMessage();
      postParentMessage(sendInvitationValidationFailedMessage());
      postParentMessage(
        invitationFormValidationFailedMessage(
          "We encountered some issues with your responses. Please review them and try again."
        )
      );
      if (
        currentWizard?.question === questions[questions?.length - 1]?.questionId // checking for last question.
      ) {
        prevHandler();
      } else {
        nextHandler();
      }
      const moveToNonMandatory: any =
        questions.filter((item: any) => item.color === false).length > 0
          ? questions.filter((item: any) => item.color === false)[0]
          : "";
      if (!!moveToNonMandatory) {
        setLocalStorageData(
          window.localStorage,
          "IsPageRefreshed",
          moveToNonMandatory?.questionId
        );
      }
    }
  };

  async function processDocumentRating(
    submissionId: string,
    invitationId: string
  ) {
    try {
      if (!submissionId || !invitationId)
        return { error: { message: "Required details missing" }, status: 500 };

      const FormFeildsData = await getformFieldsbySubmissionId({
        submissionId: submissionId,
      }).then((res: any) => res.data);

      const raraApiConfigs = FormFeildsData?.GlobalMaster[0]?.data;
      const invitation = FormFeildsData.FormSubmission[0]?.FormInvitation;

      let formFields = invitation?.Form?.FormFields;
      const companyName = invitation?.Company?.name;

      formFields = formFields.filter((ff: any) => !!ff.Answers?.length);

      if (!formFields.length) {
        return {
          error: { message: "No answers found for RARA Rating." },
          status: 500,
        };
      }

      const ratingApiBodyInputs = formFields
        .filter((ff: any) => !!ff.Answers?.length)
        .flatMap(
          (ff: any) =>
            ff.Answers[0]?.data.value.map((val: any) => ({
              documentType: ff.interfaceOptions?.rara.documentType as string,
              formfieldId: ff.id as string,
              companyName: companyName,
              file: val.value[0],
              fileId: val.value[0]?.fileId,
            })) as {
              documentType: any;
              formfieldId: any;
              companyName: string | undefined;
              file: any;
              fileId: any;
            }[]
        );

      const raraRatingApiConfig = raraApiConfigs.find(
        (config: any) => config.name === "rara-check"
      );

      if (!raraRatingApiConfig) {
        return {
          error: { message: "No RARA rating api configs found" },
          status: 500,
        };
      }

      const urlCheck = (url: any) => {
        try {
          new URL(url);
          return true;
        } catch (err) {
          return false;
        }
      };

      const ratingPromises = ratingApiBodyInputs.map(async (item: any) => {
        const isValidUrl = urlCheck(item?.file?.path);
        if (!isValidUrl) {
          return {
            error: { message: "Document url is not valid" },
            status: 500,
          };
        }

        const processSingleFileBody = {
          url: raraRatingApiConfig.url,
          document_url: item?.file?.path,
          company_name: item?.companyName,
          document_key: item?.documentType,
          auth_key: raraRatingApiConfig.authkey,
          partialSaveData: {
            invitationId: invitationId,
            submissionId: submissionId,
            formFieldId: item.formfieldId as string,
            type: "validation",
            fileId: item.fileId.toString(),
            data: null,
          },
        };

        // const {
        //   url,
        //   auth_key,
        //   company_name,
        //   document_key,
        //   document_url,
        //   partialSaveData,
        // } = processSingleFileBody;

        // const raraApiResponse = await fetch(url, {
        //   method: "POST",
        //   body: JSON.stringify({ document_url, company_name, document_key }),
        //   headers: {
        //     "Content-Type": "application/json",
        //     Authorization: auth_key,
        //   },
        // }).then((res: any) => res.json());

        // const saveData: RaraValidationAndRating_Insert_Input[] = [
        //   {
        //     ...partialSaveData,
        //     data: raraApiResponse,
        //   },
        // ];

        // const response = await insertRaraValidationAndRating({
        //   variables: {
        //     object: saveData,
        //   },
        // }).then((res) => res.data);

        const response = fetch("/api/rara/document-rating-single", {
          method: "POST",
          body: JSON.stringify(processSingleFileBody),
          headers: {
            "Content-Type": "application/json",
            Authorization: userSession?.accessToken ?? "",
          },
        });

        return response;
      });

      const ratingDocsResults = await Promise.all(ratingPromises);

      return ratingDocsResults;
    } catch (error) {
      console.error("rara rating process", error);
    }

    return null;
  }

  useEffect(() => {
    globalThis.addEventListener("message", async (event: any) => {
      event.preventDefault();
      const WarningData1 = useWarningMessageStore.getState().WarningRuleFields;
      let messageData: any;
      let dataType = typeof event.data;
      if (dataType === "string") {
        try {
          messageData = JSON.parse(event.data);
          const type = messageData.type;
          if (type === "snowkap-isRefreshPage") {
            if (messageData.questionId) {
              setQuestionId(messageData.questionId);
              setActiveTab(messageData.questionId);
              setLocalStorageData(
                window.localStorage,
                "IsPageRefreshed",
                messageData.questionId
              );
              return;
            }
          }

          const WarningRulestorequestionid = WarningData1[0]?.questionid ?? "";
          if (type === "snowkap-warningmessage") {
            if (messageData.response === true) {
              const Iswarningmessagereceived = WarningData1[0]?.isWarningRule;
              if (
                !!Iswarningmessagereceived &&
                !!Iswarningmessagereceived === true
              ) {
                const isFileUpload =
                  WarningData1?.filter((x) => !!x.isFileUpload === true)
                    .length > 0
                    ? true
                    : false;
                if (messageData.step === "prev") {
                  if (isFileUpload === false)
                    await warninglogsave(
                      query?.mode,
                      invitationId,
                      UpsertValidationWarningLogsMutation
                    );

                  // useWarningMessageStore.setState({
                  //   WarningRuleFields: []
                  // });
                  removeWarning();

                  if (typeof window !== "undefined") {
                    window?.document?.getElementById("btnPrevious")?.click();
                  }
                } else if (messageData.step === "next") {
                  // const isFileUpload =
                  //   WarningData1?.filter((x) => !!x.isFileUpload === true)
                  //     .length > 0
                  //     ? true
                  //     : false;
                  if (isFileUpload === false)
                    await warninglogsave(
                      query?.mode,
                      invitationId,
                      UpsertValidationWarningLogsMutation
                    );

                  // useWarningMessageStore.setState({
                  //   WarningRuleFields: []
                  // });
                  removeWarning();

                  if (typeof window !== "undefined") {
                    window?.document?.getElementById("btnNext")?.click();
                  }
                } else if (messageData.step === "jumptoquestionid") {
                  const gotoquestionid: any | null = getLocalStorageData(
                    window.localStorage,
                    "gotoquestionid"
                  );
                  if (isFileUpload === false)
                    await warninglogsave(
                      query?.mode,
                      invitationId,
                      UpsertValidationWarningLogsMutation
                    );
                  await upsertQuestionAnswer(WarningRulestorequestionid);
                  let warningList: warningmessageObjectType[] = [];

                  // useWarningMessageStore.setState({
                  //   WarningRuleFields: warningList
                  // });
                  removeWarning();
                  setQuestionId(gotoquestionid);
                  // setActiveTab(gotoquestionid);
                  setLocalStorageData(
                    window.localStorage,
                    "IsPageRefreshed",
                    gotoquestionid
                  );
                } else if (messageData.step === "submit") {
                  if (isFileUpload === false)
                    await warninglogsave(
                      query?.mode,
                      invitationId,
                      UpsertValidationWarningLogsMutation
                    );

                  scrollToTop();

                  // useWarningMessageStore.setState({
                  //   WarningRuleFields: []
                  // });
                  removeWarning();
                  if (typeof window !== "undefined") {
                    window?.document?.getElementById("btnSubmit")?.click();
                  }
                } else if (messageData.step === "jumptoSectionquestionid") {
                  if (isFileUpload === false)
                    await warninglogsave(
                      query?.mode,
                      invitationId,
                      UpsertValidationWarningLogsMutation
                    );
                  await upsertQuestionAnswer(WarningRulestorequestionid);

                  let warningList: warningmessageObjectType[] = [];

                  // useWarningMessageStore.setState({
                  //   WarningRuleFields: warningList
                  // });
                  removeWarning();
                  if (!!messageData.nextActivetabId) {
                    sectionHandler(messageData.nextActivetabId);
                    setCurrentSec(messageData.nextActivetabId);
                  }
                }
              }

              return;
            }
          }
        } catch (error) {}
      }
    });
  }, [getQuestionId]);

  const ApproveFormInvitationStatus = async () => {
    if (IsPageRefreshed) {
      setLocalStorageData(window.localStorage, "IsPageRefreshed", "null");
    }
    let invitationStatus: string = "";
    if (InviterAutoAppoverRecord) {
      if (
        InviterAutoAppoverRecord[0].IsEnable === true &&
        InviterAutoAppoverRecord[0].IsAutoApprove === false
      ) {
        invitationStatus = FormInvitationStatus.Approved;
      }
    }
    const success_data = await formInvitationStatusMutation({
      variables: {
        invitationId: invitationId,
        invitationStatus: invitationStatus,
      },
    }).then((rec) => {
      return rec?.data?.update_FormInvitation?.returning;
    });

    const success = success_data && success_data?.length > 0;
    if (!!success) {
      postParentMessage(warpApprovedSuccessfully());
      postParentMessage(prevListingPageredirect());
    } else {
      alert("Something went wrong.");
    }

    await fetch("/api/assessmentapproved-email1", {
      method: "POST",
      headers: {
        "content-type": "application/json",
      },
      body: JSON.stringify({
        id: invitationId,
        type: "AssessmentApprovalEmail",
        companyId:
          invitationAndSubmissionQueryResult?.FormInvitation[0]?.companyId,
        formId: invitationAndSubmissionQueryResult?.FormInvitation[0]?.formId,
        platformId: userSession?.platform?.id,
      }),
    });
  };

  const checkQuestionAnswers = (onSubmit: boolean) => {
    if (onSubmit) {
      steps.map((stepData) => {
        const step = stepData?.component?.props?.formField;
        step?.children.map((tabdata: any) => {
          tabdata?.children.map((formFieldData: any) => {
            let tabColor = isAnswered(formFieldData, false, chkStore);
            changeColorHandler(tabColor, formFieldData?.Question?.id);
          });
        });
      });
    } else {
      steps
        .filter((items) => items.key == currentSec)
        .map((stepData) => {
          const step = stepData?.component?.props?.formField;
          step?.children.map((tabdata: any) => {
            tabdata?.children.map((formFieldData: any) => {
              let tabColor = isAnswered(formFieldData, false, chkStore);
              changeColorHandler(tabColor, formFieldData?.Question?.id);
            });
          });
        });
    }
  };
  let isFirst = true;
  let isLast = false;
  if (questions) {
    isFirst = activeTab === questions[0]?.questionId;
    isLast =
      currentWizard?.question === questions[questions?.length - 1]?.questionId;
  }
  let isFirstRecomm = false;
  let isLastRecomm = false;
  if (questionsRecommForNext?.length == 0) {
    isLastRecomm = true;
  }
  if (questionsRecommForPrev?.length == 0) {
    isFirstRecomm = true;
  }
  const scrollToTop = () => {
    // for smoothly scroll to top.
    window.scroll({
      top: 0,
      left: 0,
      behavior: "smooth",
    });
  };

  const getProgressBarPercentage = () => {
    return (
      (questions?.filter(
        (v) => v.section === currentWizard.section && v.color == true
      ).length /
        questions?.filter((v) => v.section === currentWizard.section).length) *
      100
    ).toFixed(0);
  };
  const setCurrentSection = async (section: any) => {
    //localStorage.setItem("secCurrent", section);
    if (IsPageRefreshed) {
      setLocalStorageData(window.localStorage, "IsPageRefreshed", "null");
    }

    if (!!currentWizard.question && query?.mode === FormMode.Start) {
      const WarningData1 = useWarningMessageStore.getState().WarningRuleFields;

      if (WarningData1[0]?.isWarningRule) {
        setLoading(false);
        if (WarningData1[0]?.isFileUpload) {
          postParentMessage(
            raraAlertPopup(
              WarningData1[0]?.warningmessage,
              "jumptoSectionquestionid",
              section
            )
          );
        } else {
          postParentMessage(
            warpWarningmessage(
              WarningData1[0]?.warningmessage,
              "jumptoSectionquestionid"
            )
          );
        }
        return false;
      } else {
        if (section !== null) {
          await upsertQuestionAnswer(activeTab);
          sectionHandler(section);
          setCurrentSec(section);
        }
        if (section === null) setCurrentSec("");
      }
    } else {
      if (section !== null) {
        sectionHandler(section);
        setCurrentSec(section);
      }
      if (section === null) setCurrentSec("");
    }
  };
  const nextQuesBtn = async (e: any) => {
    if (IsPageRefreshed) {
      setLocalStorageData(window.localStorage, "IsPageRefreshed", "null");
    }
    scrollToTop();
    if (isLast) return;
    const WarningData1 = useWarningMessageStore.getState().WarningRuleFields;
    if (query?.mode === FormMode.Start) {
      // await upsertQuestionAnswer(activeTab);
      if (WarningData1[0]?.isWarningRule) {
        setLoading(false);
        if (WarningData1[0]?.isFileUpload) {
          postParentMessage(
            raraAlertPopup(WarningData1[0]?.warningmessage, "next")
          );
        } else {
          postParentMessage(
            warpWarningmessage(WarningData1[0]?.warningmessage, "next")
          );
        }

        return false;
      } else {
        //remove warning log if recorded
        if (WarningData1.length > 0) {
          let data = WarningData1.filter(
            (x: any) => x.ispopupmessageremoved === true
          );
          if (data.length > 0) {
            data.map((item: any) => {
              UpdateValidationWarningLogsMutation({
                variables: {
                  formfieldId: item?.formfieldid,
                  invitationId: invitationId,
                },
              });
            });
          }
        }
        //remove warning log if recorded
        await upsertQuestionAnswer(activeTab);

        let warningList: warningmessageObjectType[] = [];

        // useWarningMessageStore.setState({
        //   WarningRuleFields: warningList
        // });
        removeWarning();
      }
      ///////////upsert
    } else if (query?.mode === FormMode.ViewRecommendation) {
      // await upsertQuestionAnswer(activeTab);
      if (WarningData1[0]?.isWarningRule) {
        setLoading(false);
        postParentMessage(
          warpWarningmessage(WarningData1[0]?.warningmessage, "next")
        );
        return false;
      } else {
        //remove warning log if recorded

        if (WarningData1.length > 0) {
          let data = WarningData.filter(
            (x: any) => x.ispopupmessageremoved === true
          );
          if (data.length > 0) {
            data.map((item: any) => {
              UpdateValidationWarningLogsMutation({
                variables: {
                  formfieldId: item?.formfieldid,
                  invitationId: invitationId,
                },
              });
            });
          }
        }
        await upsertQuestionAnswer(activeTab);

        //remove warning log if recorded
      }
      ///////////upsert
    }

    let firstTabIndex =
      useGroupWizardStore
        .getState()
        .questions?.findIndex(
          (q: any) =>
            q.questionId === useGroupWizardStore.getState().current?.question
        ) ?? 0;

    let nextQuestion =
      useGroupWizardStore.getState().questions[firstTabIndex + 1].questionId;
    nextHandler();
    postParentMessage(prevNextClick(nextQuestion || "", invitationId));
    // prevNext();
  };

  const prevQuesBtn = async (e: any) => {
    if (IsPageRefreshed) {
      setLocalStorageData(window.localStorage, "IsPageRefreshed", "null");
    }
    scrollToTop();
    if (isFirst) return;

    const WarningData1 = useWarningMessageStore.getState().WarningRuleFields;
    if (query?.mode === FormMode.Start) {
      if (WarningData1[0]?.isWarningRule) {
        setLoading(false);
        if (WarningData1[0]?.isFileUpload) {
          postParentMessage(
            raraAlertPopup(WarningData1[0]?.warningmessage, "prev")
          );
        } else {
          postParentMessage(
            warpWarningmessage(WarningData1[0]?.warningmessage, "prev")
          );
        }
        return false;
      } else {
        //remove warning log if recorded

        if (WarningData1.length > 0) {
          let data = WarningData1.filter(
            (x: any) => x.ispopupmessageremoved === true
          );
          if (data.length > 0) {
            data.map((item: any) => {
              UpdateValidationWarningLogsMutation({
                variables: {
                  formfieldId: item?.formfieldid,
                  invitationId: invitationId,
                },
              });
            });
          }
        }
        //remove warning log if recorded
        await upsertQuestionAnswer(activeTab);
        let warningList: warningmessageObjectType[] = [];

        // useWarningMessageStore.setState({
        //   WarningRuleFields: warningList,
        // });
        removeWarning();
      }
      ///////////upsert
      //  await upsertQuestionAnswer(activeTab);
    } else if (query?.mode === FormMode.ViewRecommendation) {
      if (WarningData1[0]?.isWarningRule) {
        setLoading(false);

        let messagedata = postParentMessage(
          warpWarningmessage(WarningData1[0]?.warningmessage, "prev")
        );

        return false;
      } else {
        //remove warning log if recorded

        if (WarningData1.length > 0) {
          let data = WarningData1.filter(
            (x: any) => x.ispopupmessageremoved === true
          );
          if (data.length > 0) {
            data.map((item: any) => {
              UpdateValidationWarningLogsMutation({
                variables: {
                  formfieldId: item?.formfieldid,
                  invitationId: invitationId,
                },
              });
            });
          }
        }
        //remove warning log if recorded
        await upsertQuestionAnswer(activeTab);

        // useWarningMessageStore.setState({
        //   WarningRuleFields: [],

        // });
        removeWarning();
      }
    }

    let prevTabIndex =
      useGroupWizardStore
        .getState()
        .questions?.findIndex(
          (q: any) =>
            q.questionId === useGroupWizardStore.getState().current?.question
        ) ?? 0;

    let prevQuestion =
      useGroupWizardStore.getState().questions[prevTabIndex - 1]?.questionId;
    prevHandler();
    //prevNext();
    postParentMessage(prevNextClick(prevQuestion || "", invitationId));
  };
  const isEdge = useMemo(() => /Edg/.test(navigator.userAgent), []);

  const memoizedAccordion = useMemo(() => {
    const getSubSection = (
      stepChild: any,
      stepData: any,
      level: number,
      defaultAccordian: any
    ) => {
      const heirachyLevel = 7;
      let isShowHeirachy = false;
      return stepChild
        ?.filter(
          (a: any) => a.interface === "group-tabs" || a.type === "sub-theme"
        )
        .map((subStep: any) => {
          //console.log("true subStep.type", subStep?.interfaceOptions?.title);
          // if (subStep.type === "sub-theme" || subStep.type === "tabs") {
          if (
            subStep?.interfaceOptions?.title === undefined ||
            parseInt(hierarchyLevel) === 1
          ) {
            return (
              <Group
                bg={"#038FC71A"}
                p={10}
                mb={1}
                spacing={10}
                className="questionParent"
              >
                {questions.map((d, i) => {
                  if (d.section === stepData.key) {
                    return tabs.map((tab) => {
                      if (tab.title === d.title) {
                        let isAssigned: any = AssesseeUserMappingResult?.filter(
                          (x: any) =>
                            x.questionId === tab.formField?.Question?.id
                        );

                        const currentFormfields = useFormFieldStore
                          ?.getState()
                          .formFields.filter(
                            (dataItems) =>
                              dataItems.questionId ==
                              tab.formField?.Question?.id
                          );
                        return (
                          <QuestionBox
                            isSuggestion={
                              useFormFieldStore
                                ?.getState()
                                ?.Suggestions.filter(
                                  (qItems) =>
                                    currentFormfields.filter(
                                      (items) => items.id == qItems?.formFieldId
                                    ).length > 0
                                ).length > 0
                            }
                            formField={tab.formField}
                            activeTab={activeTab}
                            tabKey={tab.formField?.Question?.id}
                            section={d.section}
                            breadcrumb={subStep?.interfaceOptions?.breadcrumb}
                            nextPreviousHandler={async () => {
                              scrollToTop();
                              if (IsPageRefreshed) {
                                setLocalStorageData(
                                  window.localStorage,
                                  "IsPageRefreshed",
                                  "null"
                                );
                              }

                              //if (isLast) return; //commented to save the last question data on question switch
                              if (query?.mode === FormMode.Start) {
                                await upsertQuestionAnswer(activeTab);
                              } else if (
                                query?.mode === FormMode.ViewRecommendation
                              ) {
                                await upsertQuestionAnswer(activeTab);
                              }
                              postParentMessage(
                                prevNextClick(
                                  currentWizard?.question || "",
                                  invitationId
                                )
                              );
                            }}
                            isAssigned={
                              isAssigned?.length > 0 &&
                              userSession?.user?.role === AppRoles.Invitee
                                ? true
                                : false
                            }
                          />
                        );
                      }
                    });
                  }
                })}
              </Group>
            );
          } else {
            const TabSubthemeData = tabs.filter(
              (x) =>
                x.formField.type === "sub-theme" &&
                x.formField.subtheme === subStep.subtheme &&
                x.formField.Section?.key === subStep?.Section?.key &&
                subStep?.interfaceOptions?.title !== undefined
            );
            let AccorValue: string = "";
            if (subStep?.interfaceOptions?.breadcrumb.indexOf(">") > -1) {
              const breadcrumbArray: Array<String> =
                subStep?.interfaceOptions?.breadcrumb
                  .toString()
                  .split(">")
                  .map((x: String) => x.trim().split(" ").join("_"));
              AccorValue = breadcrumbArray.join("_");

              if (breadcrumbArray.length < heirachyLevel) {
                isShowHeirachy = true;
                level = level + 1;
              }
            } else {
              AccorValue = subStep?.interfaceOptions?.breadcrumb.trim();
            }

            // if (level < heirachyLevel) {
            //   isShowHeirachy = true;
            //   level = level + 1;
            // }

            return (
              <Accordion
                multiple={true}
                chevron={
                  subStep?.children?.length > 0 ? (
                    <IconPlus size="1rem" />
                  ) : (
                    <IconChevronDown size="1rem" />
                  )
                }
                styles={{
                  chevron: {
                    "&[data-rotate]": {
                      transform:
                        subStep?.children?.length > 0
                          ? "rotate(45deg)"
                          : "rotate(180deg)",
                    },
                  },
                  item: {
                    borderBottom: "none",
                    paddingBottom: 0,
                    paddingLeft: 0,
                    paddingRight: 0,
                  },
                  control: {
                    marginBottom: 0 + "!important",
                    "&[data-active]": {
                      background:
                        subStep?.children?.length > 0
                          ? "linear-gradient(94.76deg, #005C81 0.57%, #122F47 95%)"
                          : "#038FC71A",
                      color: subStep?.children?.length > 0 ? "#fff" : "#005C81",

                      "&:hover": {
                        backgroundColor:
                          subStep?.children?.length > 0
                            ? "#1C9689 !important"
                            : "#E7ECEB !important",
                      },
                    },
                  },
                  content: {
                    paddingBottom: 0,
                    paddingTop: subStep?.children?.length > 0 ? "0" : "0",
                  },
                }}
                // classNames={{
                //   control: classes.accordionItem,
                // }}
                value={getaccordianBreadCrumb ?? []}
                onChange={setAccordianBreadCrumb}
                defaultValue={defaultAccordian ?? []}
                // onChange={() =>
                //   handleSubTheme(subStep?.interfaceOptions?.breadcrumb)
                // }
              >
                <Accordion.Item
                  // value={subStep?.interfaceOptions?.title}
                  value={AccorValue}
                  style={{ borderBottom: "none !important" }}
                >
                  {subStep?.interfaceOptions?.title !== "" &&
                  subStep?.interfaceOptions?.title !== undefined &&
                  isShowHeirachy ? (
                    <>
                      <Accordion.Control
                        pr={5}
                        pl={10}
                        pt={8}
                        pb={8}
                        c="#969696"
                        fz={12}
                        fw={500}
                        mb={5}
                      >
                        {subStep?.interfaceOptions?.title}
                      </Accordion.Control>
                      <Accordion.Panel
                        ml={-16}
                        mr={-16}
                        className={classes.paddingbottomno}
                      >
                        {subStep?.children?.length > 0 ? (
                          getSubSection(
                            subStep?.children,
                            stepData,
                            level,
                            defaultAccordian
                          )
                        ) : (
                          <Group
                            bg={"#038FC71A"}
                            p={10}
                            mb={5}
                            spacing={10}
                            className="questionParent"
                          >
                            {TabSubthemeData.map((d) => {
                              const filterTab = tabs.filter(
                                (x) =>
                                  x.formField.type !== "sub-theme" &&
                                  x.formField.subtheme === d.title &&
                                  x.formField.Section?.key ===
                                    subStep?.Section?.key
                              );
                              return filterTab.map((tab) => {
                                let isAssigned: any =
                                  AssesseeUserMappingResult?.filter(
                                    (x: any) =>
                                      x.questionId ===
                                      tab.formField?.Question?.id
                                  );
                                const currentFormfields = useFormFieldStore
                                  ?.getState()
                                  .formFields.filter(
                                    (dataItems) =>
                                      dataItems.questionId ==
                                      tab.formField?.Question?.id
                                  );
                                return (
                                  <QuestionBox
                                    isSuggestion={
                                      useFormFieldStore
                                        ?.getState()
                                        ?.Suggestions.filter(
                                          (qItems) =>
                                            currentFormfields.filter(
                                              (items) =>
                                                items.id == qItems?.formFieldId
                                            ).length > 0
                                        ).length > 0
                                    }
                                    formField={tab.formField}
                                    activeTab={activeTab}
                                    tabKey={tab.formField?.Question?.id}
                                    section={stepData.key}
                                    breadcrumb={
                                      subStep?.interfaceOptions?.breadcrumb
                                    }
                                    nextPreviousHandler={async () => {
                                      if (IsPageRefreshed) {
                                        setLocalStorageData(
                                          window.localStorage,
                                          "IsPageRefreshed",
                                          "null"
                                        );
                                      }
                                      scrollToTop();
                                      if (isLast) return;

                                      if (query?.mode === FormMode.Start) {
                                        if (WarningData[0]?.isWarningRule) {
                                          setLoading(false);

                                          // postParentMessage(
                                          //   warpWarningmessage(
                                          //     WarningData[0]?.warningmessage,
                                          //     "jumptoquestionid"
                                          //   )
                                          // );
                                          if (WarningData[0]?.isFileUpload) {
                                            postParentMessage(
                                              raraAlertPopup(
                                                WarningData[0]?.warningmessage,
                                                "jumptoquestionid"
                                              )
                                            );
                                          } else {
                                            postParentMessage(
                                              warpWarningmessage(
                                                WarningData[0]?.warningmessage,
                                                "jumptoquestionid"
                                              )
                                            );
                                          }
                                          return false;
                                        } else {
                                          //remove warning log if recorded

                                          if (WarningData.length > 0) {
                                            let data = WarningData.filter(
                                              (x: any) =>
                                                x.ispopupmessageremoved === true
                                            );
                                            if (data.length > 0) {
                                              data.map((item: any) => {
                                                UpdateValidationWarningLogsMutation(
                                                  {
                                                    variables: {
                                                      formfieldId:
                                                        item?.formfieldid,
                                                      invitationId:
                                                        invitationId,
                                                    },
                                                  }
                                                );
                                              });
                                            }
                                          }
                                          //remove warning log if recorded
                                          await upsertQuestionAnswer(activeTab);
                                          let warningList: warningmessageObjectType[] =
                                            [];

                                          // useWarningMessageStore.setState({
                                          //   WarningRuleFields: warningList,

                                          // });
                                          removeWarning();
                                        }
                                      } else if (
                                        query?.mode ===
                                        FormMode.ViewRecommendation
                                      ) {
                                        if (WarningData[0]?.isWarningRule) {
                                          setLoading(false);

                                          return false;
                                        } else {
                                          //remove warning log if recorded

                                          if (WarningData.length > 0) {
                                            let data = WarningData.filter(
                                              (x: any) =>
                                                x.ispopupmessageremoved === true
                                            );
                                            if (data.length > 0) {
                                              data.map((item: any) => {
                                                UpdateValidationWarningLogsMutation(
                                                  {
                                                    variables: {
                                                      formfieldId:
                                                        item?.formfieldid,
                                                      invitationId:
                                                        invitationId,
                                                    },
                                                  }
                                                );
                                              });
                                            }
                                          }
                                          //remove warning log if recorded
                                          await upsertQuestionAnswer(activeTab);
                                          let warningList: warningmessageObjectType[] =
                                            [];
                                          // useWarningMessageStore.setState({
                                          //   WarningRuleFields: warningList,

                                          // });
                                          removeWarning();
                                        }
                                      }
                                      postParentMessage(
                                        prevNextClick(
                                          currentWizard?.question || "",
                                          invitationId
                                        )
                                      );
                                    }}
                                    isAssigned={
                                      isAssigned?.length > 0 &&
                                      userSession?.user?.role ===
                                        AppRoles.Invitee
                                        ? true
                                        : false
                                    }
                                  />
                                );
                              });
                            })}
                          </Group>
                        )}
                      </Accordion.Panel>
                    </>
                  ) : (
                    <>
                      {subStep?.children?.length > 0 ? (
                        getSubSection(
                          subStep?.children,
                          stepData,
                          level,
                          defaultAccordian
                        )
                      ) : (
                        <Group
                          bg={"#038FC71A"}
                          p={10}
                          mb={5}
                          spacing={10}
                          className="questionParent"
                        >
                          {TabSubthemeData.map((d) => {
                            const filterTab = tabs.filter(
                              (x) =>
                                x.formField.type !== "sub-theme" &&
                                x.formField.subtheme === d.title &&
                                x.formField.Section?.key ===
                                  subStep?.Section?.key
                            );
                            return filterTab.map((tab) => {
                              let isAssigned: any =
                                AssesseeUserMappingResult?.filter(
                                  (x: any) =>
                                    x.questionId === tab.formField?.Question?.id
                                );

                              const currentFormfields = useFormFieldStore
                                ?.getState()
                                .formFields.filter(
                                  (dataItems) =>
                                    dataItems.questionId ==
                                    tab.formField?.Question?.id
                                );
                              return (
                                <QuestionBox
                                  isSuggestion={
                                    useFormFieldStore
                                      ?.getState()
                                      ?.Suggestions.filter((qItems) =>
                                        currentFormfields.filter(
                                          (items) =>
                                            items.id == qItems?.formFieldId
                                        )
                                      ).length > 0
                                  }
                                  formField={tab.formField}
                                  activeTab={activeTab}
                                  tabKey={tab.formField?.Question?.id}
                                  section={stepData.key}
                                  breadcrumb={
                                    subStep?.interfaceOptions?.breadcrumb
                                  }
                                  nextPreviousHandler={async () => {
                                    if (IsPageRefreshed) {
                                      setLocalStorageData(
                                        window.localStorage,
                                        "IsPageRefreshed",
                                        "null"
                                      );
                                    }
                                    scrollToTop();

                                    if (isLast) return;

                                    const WarningData1 =
                                      useWarningMessageStore.getState()
                                        .WarningRuleFields;
                                    if (query?.mode === FormMode.Start) {
                                      if (WarningData1[0]?.isWarningRule) {
                                        setLoading(false);

                                        // postParentMessage(
                                        //   warpWarningmessage(
                                        //     WarningData1[0]?.warningmessage,
                                        //     "jumptoquestionid"
                                        //   )
                                        // );
                                        if (WarningData1[0]?.isFileUpload) {
                                          postParentMessage(
                                            raraAlertPopup(
                                              WarningData1[0]?.warningmessage,
                                              "jumptoquestionid"
                                            )
                                          );
                                        } else {
                                          postParentMessage(
                                            warpWarningmessage(
                                              WarningData1[0]?.warningmessage,
                                              "jumptoquestionid"
                                            )
                                          );
                                        }
                                        return false;
                                      } else {
                                        //remove warning log if recorded

                                        if (WarningData1.length > 0) {
                                          let data = WarningData1.filter(
                                            (x: any) =>
                                              x.ispopupmessageremoved === true
                                          );
                                          if (data.length > 0) {
                                            data.map((item: any) => {
                                              UpdateValidationWarningLogsMutation(
                                                {
                                                  variables: {
                                                    formfieldId:
                                                      item?.formfieldid,
                                                    invitationId: invitationId,
                                                  },
                                                }
                                              );
                                            });
                                          }
                                        }
                                        //remove warning log if recorded
                                        await upsertQuestionAnswer(activeTab);
                                        let warningList: warningmessageObjectType[] =
                                          [];
                                        // useWarningMessageStore.setState({
                                        //   WarningRuleFields: warningList,

                                        // });
                                        removeWarning();
                                      }
                                    } else if (
                                      query?.mode ===
                                      FormMode.ViewRecommendation
                                    ) {
                                      if (WarningData[0]?.isWarningRule) {
                                        setLoading(false);

                                        let messagedata = postParentMessage(
                                          warpWarningmessage(
                                            WarningData[0]?.warningmessage,
                                            "prev"
                                          )
                                        );

                                        return false;
                                      } else {
                                        //remove warning log if recorded

                                        if (WarningData.length > 0) {
                                          let data = WarningData.filter(
                                            (x: any) =>
                                              x.ispopupmessageremoved === true
                                          );
                                          if (data.length > 0) {
                                            data.map((item: any) => {
                                              UpdateValidationWarningLogsMutation(
                                                {
                                                  variables: {
                                                    formfieldId:
                                                      item?.formfieldid,
                                                    invitationId: invitationId,
                                                  },
                                                }
                                              );
                                            });
                                          }
                                        }
                                        //remove warning log if recorded
                                        await upsertQuestionAnswer(activeTab);
                                        let warningList: warningmessageObjectType[] =
                                          [];
                                        // useWarningMessageStore.setState({
                                        //   WarningRuleFields: warningList,
                                        // });
                                        removeWarning();
                                      }
                                    }
                                    postParentMessage(
                                      prevNextClick(
                                        currentWizard?.question || "",
                                        invitationId
                                      )
                                    );
                                  }}
                                  isAssigned={
                                    isAssigned?.length > 0 &&
                                    userSession?.user?.role === AppRoles.Invitee
                                      ? true
                                      : false
                                  }
                                />
                              );
                            });
                          })}
                        </Group>
                      )}
                    </>
                  )}
                </Accordion.Item>
              </Accordion>
            );
          }
          // }
        });
    };
    const bindAccordian = () => {
      return (
        <Accordion
          styles={{
            item: { borderBottom: "none", paddingBottom: 1 },
            control: {
              "&[data-active]": {
                cursor: "default",
              },
            },
            content: { paddingBottom: 0 },
          }}
          classNames={{
            control: classes.accordionItem,
          }}
          value={currentSec}
          onChange={(e) => setCurrentSection(e)}
        >
          {questions.length > 0 &&
            steps.map((stepData) => {
              const step = stepData?.component?.props?.formField;
              const setDefault = getaccordianBreadCrumb ?? [];
              return (
                <Accordion.Item value={stepData.key} key={stepData.key}>
                  <Accordion.Control
                    pr={5}
                    pl={10}
                    pt={8}
                    pb={8}
                    c="#888888"
                    fz={12}
                    bg="#ffffff"
                    lts={2}
                    style={{
                      // borderRadius: "5px",
                      textTransform: "uppercase",
                      pointerEvents:
                        stepData.key === currentSec ? "none" : "all",
                    }}
                  >
                    {step?.interfaceOptions?.title}
                  </Accordion.Control>
                  {stepData.key === currentSec ? (
                    <Accordion.Panel
                      ml={-15}
                      mr={-15}
                      className={classes.paddingbottomno}
                    >
                      {getSubSection(step?.children, stepData, 1, setDefault)}
                    </Accordion.Panel>
                  ) : (
                    <></>
                  )}
                </Accordion.Item>
              );
            })}
        </Accordion>
      );
    };

    return bindAccordian();
  }, [
    currentSec,
    steps,
    questionId,
    getaccordianBreadCrumb,
    classes,
    questions,
  ]);
  const formDetails = {
    formId: chkStore.formId,
    invitationId: chkStore.formInvitationId,
    questionId: activeTab,
    SubmissionId: getFormFieldStoreState().formSubmissionId,
  };
  const nextRecommendation = () => {
    if (IsPageRefreshed) {
      setLocalStorageData(window.localStorage, "IsPageRefreshed", "null");
    }
    const currentQuestionData = getFormFieldStoreState().formFields.find(
      (m: any) => m.Question?.id === currentWizard?.question
    );
    let nextQuestion: any = nextRecommHandler(currentQuestionData);
    if (!!nextQuestion) questionRecommHandler(nextQuestion);
    postParentMessage(prevNextClick(nextQuestion || "", invitationId));
  };
  const prevRecommendation = () => {
    if (IsPageRefreshed) {
      setLocalStorageData(window.localStorage, "IsPageRefreshed", "null");
    }
    const currentQuestionData = getFormFieldStoreState().formFields.find(
      (m: any) => m.Question?.id === currentWizard?.question
    );
    let prevQuestion: any = prevRecommHandler(currentQuestionData);

    if (!!prevQuestion) questionRecommHandler(prevQuestion);
    postParentMessage(prevNextClick(prevQuestion || "", invitationId));
  };

  const elementRef = useRef(null);

  useEffect(() => {
    if (!elementRef.current) return;

    const observer = new ResizeObserver((entries) => {
      for (let entry of entries) {
        if (entry.target === elementRef.current) {
          const newHeight = entry.contentRect.height;
          setTimeout(() => {
            postParentMessage(warpContentSize(newHeight + 20));
          }, 2000);
        }
      }
    });

    observer.observe(elementRef.current);

    // Cleanup observer on component unmount
    return () => {
      observer.disconnect();
    };
  }, []);

  return (
    <Group spacing={0} noWrap style={{ alignItems: "stretch" }}>
      <Stack ref={elementRef} style={{ flex: "0 0 70%", width: "70%" }}>
        {/* <LoadingOverlay
          style={{ minHeight: window.innerHeight, height: "100%" }}
          visible={loading}
        /> */}

        <Spinner visible={loading} />
        <Box pr={30}>
          <Flex
            justify="space-between"
            align="center"
            className={classes.breadcrumb}
          >
            <Text>
              <span className={classes.colWhite} style={{ fontWeight: "600" }}>
                {/* Sub Categoy 2 {">"} Section 2 {">"} */}
                {currentBreadCrumb}
              </span>
            </Text>
            <Flex>
              {(userSession?.user?.role === AppRoles.Invitee ||
                userSession?.user?.role === AppRoles.Responder) &&
                isDelegateQuestion &&
                query?.mode !== FormMode.ViewRecommendation &&
                !isAlreadyAssigned &&
                !isAutoFilledQuestion && (
                  <Button
                    onClick={() =>
                      postParentMessage(warpAssignQuestion(true, formDetails))
                    }
                    color={"#72D0C6"}
                    radius="xl"
                    size="xs"
                    mr={10}
                    disabled={isAlreadyAssigned}
                    className="Assignbtn-qun"
                  >
                    Assign
                  </Button>
                )}
              {getInfoIconDetail !== "" && getInfoIconDetail !== undefined ? (
                <Tooltip
                  multiline
                  position="bottom-end"
                  offset={0}
                  label={
                    <Text size="sm">
                      {/* Hover card is revealed when user hovers over target element,
                                        it will be hidden once mouse is not over both target and
                                        dropdown elements */}
                      <Box
                        dangerouslySetInnerHTML={{
                          __html: getInfoIconDetail ?? "",
                        }}
                      />
                    </Text>
                  }
                >
                  <ActionIcon
                    style={{
                      position: "relative",
                      zIndex: 9,
                      marginRight: "20px",
                    }}
                    variant="transparent"
                  >
                    <InfoIcon color="#ffffff" size={20} />
                  </ActionIcon>
                </Tooltip>
              ) : (
                // <HoverCard
                //   width={400}
                //   withArrow
                //   shadow="md"
                //   position="bottom-end"
                //   offset={-1}
                //   arrowOffset={8}
                // >
                //   <HoverCard.Target>
                //     <ActionIcon
                //       style={{
                //         position: "relative",
                //         zIndex: 9,
                //         marginRight: "20px",
                //       }}
                //       variant="transparent"
                //     >
                //       <InfoIcon color="#ffffff" size={20} />
                //     </ActionIcon>
                //   </HoverCard.Target>
                //   <HoverCard.Dropdown>
                //     <Text size="sm">
                //       {/* Hover card is revealed when user hovers over target element,
                //   it will be hidden once mouse is not over both target and
                //   dropdown elements */}
                //       <Box
                //         dangerouslySetInnerHTML={{
                //           __html: getInfoIconDetail ?? "",
                //         }}
                //       />
                //     </Text>
                //   </HoverCard.Dropdown>
                // </HoverCard>
                ""
              )}
            </Flex>
          </Flex>
          <Tabs
            keepMounted={false}
            defaultValue={defaultQuestion[0].formField?.Question?.id}
            radius={0}
            value={activeTab}
            styles={tabsStyles}
            className="assessmentDetails-page"
          >
            {defaultQuestion.map((tab) => (
              <MediaQuery
                query="(max-width: 1366px)"
                styles={{ minHeight: "280px !important" }}
              >
                <Tabs.Panel
                  key={tab.formField?.Question?.id}
                  mih={350}
                  value={tab.formField?.Question?.id}
                  pt="xs"
                  pr={45}
                >
                  <Box sx={{ position: "relative" }}>
                    {/* {(query?.mode === FormMode.View ||
                      query?.mode === FormMode.ViewRecommendation) && (
                      <Overlay opacity={0} zIndex={5} blur={0} />
                    )} */}
                    <Group
                      noWrap
                      spacing={10}
                      align="flex-start"
                      style={
                        query?.mode === FormMode.View ||
                        query?.mode === FormMode.ViewRecommendation
                          ? { pointerEvents: "none" }
                          : { pointerEvents: "all" }
                      }
                    >
                      <Title
                        style={{
                          fontWeight: "600",
                          fontSize: "18px",
                          color: "#162F4B",
                        }}
                        size={16}
                      >
                        {
                          questions?.filter(
                            (d) => d.questionId === activeTab
                          )[0]?.title
                        }
                      </Title>
                      <Box style={{ width: "100%" }}>
                        <FormFieldRender formField={tab.formField} />
                      </Box>
                    </Group>
                  </Box>
                </Tabs.Panel>
              </MediaQuery>
            ))}
          </Tabs>
        </Box>
        <Flex align="center" style={{ justifyContent: "space-between" }}>
          <Group mt={20}>
            <Button
              id="btnPrevious"
              leftIcon={
                <IconChevronLeft size="1rem" style={{ marginRight: "-8px" }} />
              }
              pl={7}
              radius={40}
              disabled={isFirst}
              hidden={isFirst}
              onClick={async (e: any) => {
                prevQuesBtn(e);
              }}
              color="outlineBtn"
            >
              Prev
            </Button>
            <Button
              id="btnNext"
              //
              //
              rightIcon={
                <IconChevronRight size="1rem" style={{ marginLeft: "-8px" }} />
              }
              loading={loading ? true : false}
              pr={7}
              radius={40}
              disabled={isLast}
              hidden={isLast}
              onClick={async (e: any) => {
                nextQuesBtn(e);
              }}
              color="solidBtn"
            >
              Next
            </Button>
            {query?.mode !== FormMode.View &&
              query?.mode !== FormMode.ViewRecommendation && (
                <Button
                  id="btnSubmit"
                  radius={40}
                  onClick={() => {
                    if (IsPageRefreshed) {
                      setLocalStorageData(
                        window.localStorage,
                        "IsPageRefreshed",
                        "null"
                      );
                    }
                    const WarningData1 =
                      useWarningMessageStore.getState().WarningRuleFields;
                    if (!!WarningData1[0]?.isWarningRule) {
                      // postParentMessage(
                      //   warpWarningmessage(
                      //     WarningData1[0]?.warningmessage,
                      //     "submit"
                      //   )
                      // );
                      if (WarningData1[0]?.isFileUpload) {
                        postParentMessage(
                          raraAlertPopup(
                            WarningData1[0]?.warningmessage,
                            "submit"
                          )
                        );
                      } else {
                        postParentMessage(
                          warpWarningmessage(
                            WarningData1[0]?.warningmessage,
                            "submit"
                          )
                        );
                      }
                      return false;
                    } else {
                      scrollToTop();
                      SubmitDetails(activeTab, setLoading, loading);
                      //remove warning log if recorded

                      if (WarningData1.length > 0) {
                        let data = WarningData.filter(
                          (x: any) => x.ispopupmessageremoved === true
                        );
                        if (data.length > 0) {
                          data.map((item: any) => {
                            UpdateValidationWarningLogsMutation({
                              variables: {
                                formfieldId: item?.formfieldid,
                                invitationId: invitationId,
                              },
                            });
                          });
                        }
                      }
                      //remove warning log if recorded
                    }
                  }}
                  color="solidBtn"
                >
                  {userSession?.user?.role === AppRoles.Approver
                    ? "Approve"
                    : "Submit"}
                </Button>
              )}
            {isDevelopmentMode && (
              <Button
                radius={40}
                color="outlineBtn"
                onClick={() => {
                  console.log("allStoreValue", {
                    formFieldStore: useFormFieldStore?.getState(),
                    warninglogStore: useWarningMessageStore.getState(),
                    groupWizardStore: useGroupWizardStore.getState(),
                    currentQuestion: currentWizard?.question,
                    Suggestion: useFormFieldStore?.getState()?.Suggestions,
                    Answer: useFormFieldStore?.getState()?.answer,
                    formField: formField,
                  });
                }}
              >
                Get Store
              </Button>
            )}
            <Button
              radius={40}
              color="outlineBtn"
              onClick={() => {
                if (IsPageRefreshed) {
                  setLocalStorageData(
                    window.localStorage,
                    "IsPageRefreshed",
                    "null"
                  );
                }
                postParentMessage(invitationFormCancelMessage());
              }}
            >
              Cancel
            </Button>

            {IsApprove &&
            FormSubmissionsStatus &&
            FormSubmissionsStatus?.status === "Submitted" ? (
              InviterAutoAppoverRecord &&
              InviterAutoAppoverRecord[0]?.IsAutoApprove === false ? (
                <Button
                  // style={{
                  //   borderRadius: "20px",
                  //   backgroundColor: "#ff9e1b",
                  // }}
                  onClick={() => ApproveFormInvitationStatus()}
                  color="solidBtn"
                >
                  Approve
                </Button>
              ) : (
                <></>
              )
            ) : (
              <></>
            )}
            {(query?.mode === FormMode.View ||
              query?.mode === FormMode.ViewRecommendation) &&
              !!questionrecomm &&
              questionrecomm.length > 0 &&
              !!FormHasRecommendation &&
              FormHasRecommendation?.length > 0 && (
                <>
                  <Button
                    radius={40}
                    color="outlineBtn"
                    disabled={isFirstRecomm}
                    hidden={isFirstRecomm}
                    onClick={() => prevRecommendation()}
                  >
                    Previous Recommendation
                  </Button>
                  <Button
                    radius={40}
                    disabled={isLastRecomm}
                    hidden={isLastRecomm}
                    onClick={() => nextRecommendation()}
                    color="outlineBtn"
                  >
                    Next Recommendation
                  </Button>
                </>
              )}
          </Group>
        </Flex>
      </Stack>
      <Stack
        className="assessmentDetails-inner-right"
        align="center"
        justify="flex-start"
        p={0}
        bg={"#fff"}
        style={{
          flex: "0 0 30%",
          position: "fixed",
          right: 0,
          maxWidth: "30%",
          borderRadius: "10px",
          borderColor: "1px solid #99A7AD4D",
        }}
      >
        <Stack className="assessmentDetails-tab-stack">
          <Box
            style={{
              padding: isEdge ? "19px" : "18px",
              background: "#fff",
              borderRadius: "10px",
              maxWidth: "320px",
            }}
          >
            <Title
              align="center"
              mb={8.3}
              lh={"15.22px"}
              weight={500}
              size={12}
            >
              {getProgressBarPercentage()} % Completed
            </Title>
            <Progress
              radius="md"
              size={6}
              value={parseInt(getProgressBarPercentage())}
              styles={{
                bar: {
                  background:
                    "linear-gradient(90deg, #84D8D2 0%, #1C9689 100%)",
                },
              }}
            />
            {/* <Text color="teal" weight="500">
              Jump to
            </Text>
            <Divider mt={5} mb={5} color="#DAF2EF" />  */}
            <Group spacing={5} mt={15} mb={15}>
              {useFormFieldStore?.getState()?.isAIDataPointsAdded &&
              formAIDetailData.length > 0 ? (
                <Group align="center" spacing={6} mr={10}>
                  <IconCircle
                    fill="#005C81"
                    style={{ stroke: "#fff" }}
                    size={11}
                  />
                  <Text fz={10} fw={400}>
                    AI Suggestions
                  </Text>
                </Group>
              ) : (
                <></>
              )}
              <Group align="center" spacing={2} mr={10}>
                <IconCircle
                  fill="#25D140"
                  style={{ stroke: "#fff" }}
                  size={11}
                />
                <Text fz={10} fw={400}>
                  Submitted
                </Text>
              </Group>
              <Group align="center" spacing={6} mr={10}>
                <IconCircle
                  fill="#000000"
                  style={{ stroke: "#fff" }}
                  size={11}
                />
                <Text fz={10} fw={400}>
                  Active
                </Text>
              </Group>
              <Group align="center" spacing={6} mr={10}>
                <IconCircle
                  fill="#BBBABA"
                  style={{ stroke: "#fff" }}
                  size={11}
                />
                <Text fz={10} fw={400}>
                  Partially Submitted
                </Text>
              </Group>
              <Group align="center" spacing={6}>
                <IconCircle
                  fill="#FC4E4E"
                  style={{ stroke: "#fff" }}
                  size={11}
                />
                <Text fz={10} fw={400}>
                  Mandatory
                </Text>
              </Group>

              {userSession?.user?.role === AppRoles.Invitee &&
                isDelegateQuestion && (
                  <Group align="center" spacing={6}>
                    <IconCircle
                      fill="#B765EA"
                      style={{ stroke: "#fff" }}
                      size={11}
                    />
                    <Text fz={10} fw={400}>
                      Assigned
                    </Text>
                  </Group>
                )}
            </Group>
            <Box
              style={{
                maxHeight: "415px",
                overflowY: "auto",
                marginRight: -18,
                paddingRight: 18,
              }}
            >
              {memoizedAccordion}
            </Box>
          </Box>
        </Stack>
      </Stack>
    </Group>
  );
};
export default GroupTab;
