import {
  IngestData,
  InvitationAIStatus,
  invitationFormDetails,
  multipleFileUploadClick,
} from "@warp/shared/constants/app.constants";
export const cancelInvitationMessage = () => {
  return JSON.stringify({
    type: "warp-new-invitation-cancel",
    data: true,
  });
};

export const sendInvitationLoadingStartedMessage = () => {
  return JSON.stringify({
    type: "warp-new-invitation-started",
    data: {
      loading: true,
    },
  });
};

export const sendInvitationResponseMessage = (
  isSuccess?: Boolean,
  formDetails?: invitationFormDetails[]
) => {
  return JSON.stringify({
    type: "warp-new-invitation-finished",
    data: {
      loading: false,
      isSuccess: isSuccess,
      formDetails: formDetails,
    },
  });
};

export const sendQuestionAssignedResponseMessage = (isSuccess?: Boolean) => {
  return JSON.stringify({
    type: "warp-new-question-assigned-finished",
    data: {
      loading: false,
      isSuccess: isSuccess,
    },
  });
};

export const refreshQuestion = (questionId: string) => {
  return JSON.stringify({
    type: "warp-refresh-question",
    data: {
      questionId: questionId,
    },
  });
};
export const refreshQuestionAfterComment = (questionId: string) => {
  return JSON.stringify({
    type: "warp-refresh-question-after-comment",
    data: {
      questionId: questionId,
    },
  });
};

export const showRecommendationButton = () => {
  return JSON.stringify({
    type: "warp-show-recommendation-button",
    data: {
      isRecommendationIcon: true,
    },
  });
};

export const sendInvitationValidationFailedMessage = () => {
  return JSON.stringify({
    type: "warp-new-invitation-validation-failed",
    data: {
      loading: false,
    },
  });
};

export const prevNextClick = (
  questionId: string,
  invitationId: string,
  questionWithCommentsCount: number = 0
) => {
  return JSON.stringify({
    type: "warp-prevNextClick",
    questionId: questionId,
    questionWithCommentsCount: questionWithCommentsCount,
    invitationId: invitationId,
  });
};

export const prevListingPageredirect = () => {
  return JSON.stringify({
    type: "warp-prevListingPageredirect",
  });
};

export const startInvitationMessage = (
  InvitationId: String,
  AssessmentFormName: string,
  ComapnyName: string,
  isDataCurationSkipped: boolean,
  AIStatus: InvitationAIStatus,
  invitationStatus: String,
  AIBulkDocumentProcessings: Record<string, any>[],
  Sources: Record<string, any>[]
) => {
  return JSON.stringify({
    type: "warp-invitation-list-respond",
    data: {
      invitationId: InvitationId,
      assessmentFormName: AssessmentFormName,
      comapnyName: ComapnyName,
      isDataCurationSkipped: isDataCurationSkipped,
      AIStatus: AIStatus,
      invitationStatus: invitationStatus,
      AIBulkDocumentProcessings: AIBulkDocumentProcessings,
      Sources: Sources,
    },
  });
};

export const invitationFormStartMessage = (
  InvitationId: string,
  questionId: string,
  toPopup: boolean,
  docWithAI: boolean
) => {
  return JSON.stringify({
    type: "warp-invitation-form-start",
    data: {
      invitationId: InvitationId,
      questionId: questionId,
      toPopup: toPopup,
      docWithAI: docWithAI,
    },
  });
};

export const invitationFormquestionredirect = (
  InvitationId: string,
  questionId: string
) => {
  return JSON.stringify({
    type: "warp-invitation-form-question-redirect",
    data: {
      invitationId: InvitationId,
      questionId: questionId,
    },
  });
};

export const invitationFormSubmitMessage = () => {
  return JSON.stringify({
    type: "warp-invitation-form-submit",
    data: null,
  });
};

export const warpApprovedSuccessfully = () => {
  return JSON.stringify({
    type: "warp-approved-successfully",
    data: null,
  });
};

export const invitationFormValidationFailedMessage = (message: string) => {
  return JSON.stringify({
    type: "warp-invitation-form-validation-failed",
    data: {
      loading: false,
      message: message,
    },
  });
};

export const viewInvitationMessage = (
  InvitationId: String,
  AssessmentFormName: string,
  ComapnyName: string,
  isRecommendationIcon: boolean,
  period: string,
  requestedFrom: string,
  questionid: string,
  DeviationCount: number,
  IsCarryForward: Boolean,
  SubmissionId: string,
  Score: string
) => {
  return JSON.stringify({
    type: "warp-invitation-list-view",
    data: {
      invitationId: InvitationId,
      assessmentFormName: AssessmentFormName,
      comapnyName: ComapnyName,
      isRecommendationIcon: isRecommendationIcon,
      period: period,
      requestedFrom: requestedFrom,
      questionid: questionid,
      DeviationCount,
      IsCarryForward,
      SubmissionId,
      Score,
    },
  });
};
export const viewRecommendationInvitationMessage = (
  InvitationId: String,
  AssessmentFormName: string,
  ComapnyName: string,
  internalAssessmentCompanyName: string,
  isRecommendationIcon: boolean,
  period: string,
  requestedFrom: string,
  DeviationCount: number,
  IsCarryForward: Boolean,
  SubmissionId: string,
  Score: string
) => {
  return JSON.stringify({
    type: "warp-recommendation-invitation-list-view",
    data: {
      invitationId: InvitationId,
      assessmentFormName: AssessmentFormName,
      comapnyName: ComapnyName,
      internalAssessmentCompanyName: internalAssessmentCompanyName,
      isRecommendationIcon: isRecommendationIcon,
      period: period,
      requestedFrom: requestedFrom,
      deviationCount: DeviationCount,
      isCarryForward: IsCarryForward,
      submissionId: SubmissionId,
      score: Score,
    },
  });
};

export const invitationFormCancelMessage = () => {
  return JSON.stringify({
    type: "warp-invitation-form-cancel",
    data: null,
  });
};

export const warpContentSize = (height: Number) => {
  return JSON.stringify({
    type: "warp-content-resize",
    data: { height: height },
  });
};

export const warpShowHideCommentList = (
  IsShow: boolean,
  IsShowComment: boolean,
  IsEditComment: boolean,
  totalCommentsOnQuestions: number,
  totalCommentButton: number,
  isAllowAssignQuestion: boolean
) => {
  return JSON.stringify({
    type: "warp-ShowHide-CommentList",
    data: {
      IsShow: IsShow,
      IsShowComment: IsShowComment,
      IsEditComment: IsEditComment,
      totalQuestions: totalCommentsOnQuestions,
      totalCommentButton: totalCommentButton,
      isAllowAssignQuestion: isAllowAssignQuestion,
    },
  });
};

export const warpReopenAssessmentSubmit = (
  ClosePopUp: boolean,
  IsShowComment: boolean,
  ShowSuccessMessage: boolean
) => {
  return JSON.stringify({
    type: "warp-Reopen-Assessment",
    data: {
      closePopUp: ClosePopUp,
      isShowComment: IsShowComment,
      showSuccessMessage: ShowSuccessMessage,
    },
  });
};

export const warpClosePopup = (isClose: boolean) => {
  return JSON.stringify({
    type: "warp-ClosePopup",
    data: { isClose: isClose },
  });
};

export const warpShowLastComment = (InvitationId: string) => {
  return JSON.stringify({
    type: "warp-last-show-comments",
    data: { InvitationId: InvitationId },
  });
};
export const warpAssignQuestion = (
  isOpenedPopup: boolean,
  formDetails: object = {}
) => {
  return JSON.stringify({
    type: "warp-AssignQuestion",
    data: {
      title: "Assign Question",
      isOpenedPopup: isOpenedPopup,
      formDetails,
    },
  });
};

export const warpQuestionnaireReport = (
  isOpenedPopup: boolean,
  invitationId: string
) => {
  return JSON.stringify({
    type: "warp-QuestionnaireReport",
    data: {
      title: "Questionnaire Report",
      isOpenedPopup: isOpenedPopup,
      invitationId: invitationId,
    },
  });
};
export const warpShowComment = (
  isFormField: boolean,
  formFieldId: string,
  formfieldcommentscount: any[],
  questionId: any
) => {
  return JSON.stringify({
    type: "warp-show-comments",
    data: {
      isFormField: isFormField,
      formFieldId: formFieldId,
      formfieldcommentscount: formfieldcommentscount,
      questionId: questionId,
    },
  });
};
export const sendCommentsButtonCount = (formfieldcommentscount: number) => {
  return JSON.stringify({
    type: "send-comments-buttoncount",
    data: {
      formfieldcommentscount: formfieldcommentscount,
    },
  });
};

export const warpAddRecommendationPopup = (
  isOpenedPopup: boolean,
  formDetails: object = {},
  title: String,
  isSubmit: boolean
) => {
  return JSON.stringify({
    type: "warp-Add-Recommendation",
    data: {
      title: title,
      isOpenedPopup: isOpenedPopup,
      formDetails,
      isSubmit: isSubmit,
    },
  });
};

export const RecommendationListionPageMessage = (
  invitationId: String,
  Questionnare: String,
  Period: String,
  ComapnyName: string,
  DeviationCount: number,
  IsCarryForward: any,
  SubmissionId: string,
  Score: string
) => {
  return JSON.stringify({
    type: "warp-Recommendation-Listing",
    data: {
      invitationId: invitationId,
      questionnare: Questionnare,
      period: Period,
      comapnyName: ComapnyName,
      deviationCount: DeviationCount,
      isCarryForward: IsCarryForward,
      submissionId: SubmissionId,
      score: Score,
    },
  });
};

export const PageReload = () => {
  return JSON.stringify({
    type: "warp-page-reload",
    data: "redirect-to-listing",
  });
};
export const warpApprovedRecommendationMessage = (isSubmit: boolean) => {
  return JSON.stringify({
    type: "warp-Recommendation-Approved",
    data: {
      isSubmit: isSubmit,
    },
  });
};

export const ProgressReportMessage = (
  invitationId: String,
  Questionnare: String,
  Period: String,
  ComapnyName: string,
  SubmissionId: string,
  DeviationCount: number,
  IsCarryForward: Boolean
) => {
  return JSON.stringify({
    type: "Progress-Report-Message",
    data: {
      invitationId: invitationId,
      questionnare: Questionnare,
      period: Period,
      comapnyName: ComapnyName,
      submissionId: SubmissionId,
      deviationCount: DeviationCount,
      isCarryForward: IsCarryForward,
    },
  });
};

export const ProgressReportMessageRecommendation = (
  invitationId: String,
  Questionnare: String,
  Period: String,
  ComapnyName: string,
  SubmissionId: string,
  DeviationCount: number,
  IsCarryForward: Boolean
) => {
  return JSON.stringify({
    type: "Progress-Report-Message-Recommendation",
    data: {
      invitationId: invitationId,
      questionnare: Questionnare,
      period: Period,
      comapnyName: ComapnyName,
      submissionId: SubmissionId,
      deviationCount: DeviationCount,
      isCarryForward: IsCarryForward,
    },
  });
};

export const warpWarningmessage = (message: any, step: any) => {
  return JSON.stringify({
    type: "warp-warninmessage",
    data: {
      loading: false,
      message: message,
      step: step,
    },
  });
};
export const warpDeviationReport = (
  invitationId: string,
  deviationCount: number,
  Questionnare: String,
  Period: String,
  ComapnyName: string
) => {
  return JSON.stringify({
    type: "warp-DeviationReport",
    data: {
      title: "Deviation Report",
      invitationId: invitationId,
      deviationCount: deviationCount,
      questionnare: Questionnare,
      period: Period,
      comapnyName: ComapnyName,
    },
  });
};
export const raraAlertPopup = (
  message?: string,
  step?: string,
  nextActivetabId?: string
) => {
  return JSON.stringify({
    type: "document-validation-alert",
    data: {
      type: "warning",
      heading: "Incorrect Document Uploaded",
      message: message,
      step: step,
      nextActivetabId: nextActivetabId,
    },
  });
};

export const raraGetTokenDetails = () => {
  return JSON.stringify({
    type: "rara-get-token-details",
  });
};

export const closeMainLoader = () => {
  return JSON.stringify({
    type: "close-main-loader",
  });
};
export const ProcessSkipAndContinue = (
  isSkip: boolean,
  isContinue: boolean,
  isProcessDoc: boolean,
  ingestData: IngestData,
  docWithAI: boolean
) => {
  return JSON.stringify({
    type: "enable-disable-skip-and-continue",
    data: {
      isSkip: isSkip,
      isContinue: isContinue,
      isProcessDoc: isProcessDoc,
      ingestData: ingestData,
      docWithAI: docWithAI,
    },
  });
};
export const deleteFileModal = (
  configId: string,
  fileUrl: string,
  sourceFileId: string,
  sourceId: string,
  submissionId: string,
  formId: string,
  cardArray: Record<string, any>[],
  status: string
) => {
  return JSON.stringify({
    type: "delete-file-modal",
    data: {
      configId: configId,
      sourceFileId: sourceFileId,
      fileUrl: fileUrl,
      sourceId: sourceId,
      cardArray: cardArray,
      submissionId: submissionId,
      formId: formId,
      status: status,
    },
  });
};
export const deleteFileModaltrue = (
  configId: string,
  fileUrl: string,
  sourceFileId: string
) => {
  return JSON.stringify({
    type: "delete-file-modal-true",
    data: {
      configId: configId,
      sourceFileId: sourceFileId,
      fileUrl: fileUrl,
    },
  });
};
export const viewUploadDocumentPage = (actionName: string) => {
  return JSON.stringify({
    type: "warp-view-upload-document-page",
    data: { actionName: actionName },
  });
};

export const gotoUploadDocs = (
  invitationId: string,
  assessmentFormName: string,
  comapnyName: String,
  docWithAI: boolean
) => {
  return JSON.stringify({
    type: "go-to-upload-docs",
    data: {
      invitationId: invitationId,
      assessmentFormName: assessmentFormName,
      comapnyName: comapnyName,
      docWithAI: docWithAI,
    },
  });
};

export const suggestionPopup = (
  formFieldId: string,
  selectedId: string,
  isFile: boolean
) => {
  return JSON.stringify({
    type: "suggestions_popup",
    data: {
      formFieldId: formFieldId,
      selectedId: selectedId,
      isFile: isFile,
    },
  });
};
export const setSuggestionStoreValueFromPopup = (
  suggestionId: string,
  value: string | string[] | Record<string, any>[],
  isSelected: boolean,
  selectedData: multipleFileUploadClick[]
) => {
  return JSON.stringify({
    type: "set-store-suggestion-value-from-popup",
    data: {
      suggestionId: suggestionId,
      value: value,
      isSelected: isSelected,
      selectedData: selectedData,
    },
  });
};
export const calculatePercentage = () => {
  return JSON.stringify({
    type: "calculate-percentage",
  });
};

export const warpPopupIframeContentSize = (height: Number) => {
  return JSON.stringify({
    type: "warp-popupIframeContent-resize",
    data: { height: height },
  });
};
export const hideprocessButton = (isHide: boolean) => {
  return JSON.stringify({
    type: "hide-process-button",
    data: { isHide: isHide },
  });
};
