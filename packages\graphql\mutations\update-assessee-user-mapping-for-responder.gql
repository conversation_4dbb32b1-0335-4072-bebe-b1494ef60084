mutation UpdateAssesseeUserMappingForResponder(
  $invitationId: uuid!
  $invitationStatus: String!
  $userId: uuid!
  $questionId: uuid!
  $isResponder: Boolean
) {
  update_AssesseeUserMapping(
    _set: {
      Status: $invitationStatus
      updated_by: $userId
      IsResponder: $isResponder
    }
    where: {
      _and: [
        { InvitationId: { _eq: $invitationId } }
        { questionId: { _eq: $questionId } }
      ]
    }
  ) {
    affected_rows
  }
}
