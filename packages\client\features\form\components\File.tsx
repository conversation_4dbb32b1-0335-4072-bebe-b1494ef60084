import {
  <PERSON>Icon,
  Anchor,
  Badge,
  Box,
  Divider,
  FileInput,
  Group,
  LoadingOverlay,
  Stack,
  Text,
} from "@mantine/core";
import { MIME_TYPES } from "@mantine/dropzone";
import { IconPencil, IconPlus, IconTrash, IconX } from "@tabler/icons";
import { useUserSession } from "@warp/client/hooks/use-user-session";
import { useGetCompanyDetailByIdQuery } from "@warp/graphql/queries/generated/get-companydetail-by-id";
import { useRaraCompanyAccessQuery } from "@warp/graphql/queries/generated/get-rara-features-access-companyid";
import { useGetSourceDataByInvitationIdQuery } from "@warp/graphql/queries/generated/get-sourcedata-by-invitationId";
import {
  AICArouselData,
  AppRoles,
  blankCheck,
  FormMode,
  multipleFileUploadClick,
  RecommendationStatus,
} from "@warp/shared/constants/app.constants";
import AISuggestionCarousel from "@warp/web/pages/embed/AIBasedSections/Common/AISuggestionCarousel";
import axios from "axios";
import jsonata from "jsonata";
import { cloneDeep } from "lodash";
import { useRouter } from "next/router";
import { memo, useEffect, useRef, useState } from "react";
import { FormFieldRender } from "..";
import { useFromFileUpload } from "../../../hooks/use-form-file-upload";
import UploadFileSvgIcon from "../../../icons/FileUploadIcon";
import SampleFileIcon from "../../../icons/SampleFileIcon";
import { raraGetTokenDetails } from "../../../services/platform-window-message.service";
import {
  getAISuggestionCarouselforFileData,
  urlToFile,
} from "../common-functions";
import { convertInterfaceValueToString } from "../converter";
import {
  getChildFieldStateName,
  getChildFieldStateValue,
  setChildFieldState,
  useChildFieldState,
  useFormFieldControl,
  useFormFieldRemoveAnswerOnEnableFalse,
  useFormFieldStore,
  useInterimAnswerStore,
  useRatingValidationStore,
  useWarningMessageStore,
} from "../store";
import { FormFieldControl } from "../types";
import {
  FilevalidationErrorMessage,
  validationErrorMessage,
} from "../validation.service";
import AddRecommendationButton from "./AddRecommendation";
import CommonRecommendation from "./CommonRecommendation";
import CommonTable from "./CommonTable";
import DisplayLabel from "./DisplayLabel";
let setErrorMessagedetails = false;
let errmsg: any = [];
const postParentMessage = (message: string) =>
  window.parent?.postMessage(message, "*");
const FileField: FormFieldControl = ({ formField }) => {
  let documentName = formField.interfaceOptions?.rara?.documentName;
  let isValidDate = formField.interfaceOptions?.rara?.isValidDate || false;
  const userSession = useUserSession();
  const { query } = useRouter();
  const state = useFormFieldControl<"file">(formField);
  setErrorMessagedetails = state.setErrorMessage;
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [files, setFiles] = useState<File | null>(null);
  const [temporaryFiles, setTemporaryFiles] = useState<
    multipleFileUploadClick[]
  >([]);
  const Rating: any = useRatingValidationStore(
    (store) => store.RatingValidation
  );
  const [clickedFileIds, setClickedFileIds] = useState<
    multipleFileUploadClick[]
  >([]);
  const recommendationNewResponce: any = userSession?.GlobalMaster?.filter(
    (x: any) => x.type === "Recommendation_new"
  );
  const raraCompanyId: any = userSession?.GlobalMaster?.filter(
    (x: any) => x.type === "RaraIntegrationAccess"
  );

  const { data: raraCompanies } = useRaraCompanyAccessQuery({
    variables: {
      companyIdList: raraCompanyId[0]?.data[0]?.companyId,
      companyId: userSession?.company?.id,
    },
  });
  let companyId: any = raraCompanies?.ParentCompanyMapping?.filter(
    (rec: any) => rec.CompanyId === userSession?.company?.id
  )[0]?.CompanyId;

  const { data: companyDetails } = useGetCompanyDetailByIdQuery({
    variables: {
      id: userSession?.company?.id,
    },
  });
  const { data: pageLoadSourceFileData } = useGetSourceDataByInvitationIdQuery({
    variables: {
      invitationId: query?.invitationId,
    },
  });
  let raraCompanyName = companyDetails?.Company[0]?.name;
  // localStorage.setItem("companyId", companyId)
  const currentRating = Rating?.RaraValidationAndRating?.filter(
    (item: any) => item.formFieldId === formField.id
  );
  let FormHasRecommendation: any =
    recommendationNewResponce?.GlobalMaster?.length > 0
      ? recommendationNewResponce?.GlobalMaster[0]?.data.filter(
          (rec: any) => rec.FormId === formField?.formId
        )
      : [];

  type typeGetTokendetails = {
    token?: string;
    serviceURL?: string;
  };

  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);
  const [editId, setEditId] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const { uploadFile } = useFromFileUpload();
  const [fileerror, setfileerror] = useState("");
  const [getTokendetails, setTokendetails] = useState<typeGetTokendetails>({});

  const childFields = formField.children?.map((m: any) => m.field) ?? [];
  const hasAdditinalData = !!childFields.length;
  const answerFiles = Array.isArray(state.value) ? state.value : [];
  const removeWarning = useWarningMessageStore(
    (store) => store.removeWarningRuleFields
  );

  const inputRef = useRef<HTMLButtonElement>(null);
  useEffect(() => {
    if (!getTokendetails.hasOwnProperty("token")) {
      postParentMessage(raraGetTokenDetails());
    }

    globalThis.addEventListener("message", async (event: any) => {
      event.preventDefault();
      let messageData: any;
      let dataType = typeof event.data;
      if (dataType === "string") {
        try {
          messageData = JSON.parse(event.data);
          const type = messageData.type;
          if (type === "snowkap-tokendetails") {
            setTokendetails({
              token: messageData.token,
              serviceURL: messageData.serviceurl,
            });
          }
        } catch (error) {}
      }
    });
  }, [getTokendetails]);

  const clearFields = () => {
    formField.children?.map((field: any) =>
      setChildFieldState(formField, field, undefined)
    );
    setEditId(null);
    setUploadedFiles([]);
    setFiles(null);
    setfileerror("");
    setClickedFileIds([]);
  };

  const uploadFiles = async (id: string, file: File) => {
    setLoading(true);
    try {
      const fileResult = await uploadFile(file, false, "");

      if (!!fileResult) {
        if (!!id && id !== "") {
          if (
            clickedFileIds.filter((items) => items.sourceId == id).length > 0
          ) {
            setClickedFileIds(
              clickedFileIds.filter((items) => items.sourceId != id)
            );
          } else {
            setClickedFileIds([{ sourceId: id, filepath: fileResult?.path }]);
          }
        } else {
          if (temporaryFiles.length > 0) {
            setClickedFileIds(
              clickedFileIds.filter(
                (items) => items.sourceId != temporaryFiles[0].sourceId
              )
            );
          }
        }

        //override previoulsy selected file
        // setUploadedFiles([fileResult]);
        setUploadedFiles([fileResult]);
      }

      setFiles(null);
    } catch (error) {
      console.log("SimpleFileField", error);
    }
    setLoading(false);
  };

  const buildAnswerFileObj = (_id: number, files: any[]) => {
    let newFile = { value: files };
    newFile = formField.children?.reduce((acc: any, curr: any) => {
      acc["_id"] = _id;
      acc[curr.field] = {
        value: getChildFieldStateValue(formField, curr),
      };
      return acc;
    }, newFile);
    return newFile;
  };

  const saveFiles = async () => {
    if (!uploadedFiles.length) return;
    let newAnswer = [];
    if (editId) {
      newAnswer = answerFiles.map((file) => {
        if (file._id === editId) {
          return buildAnswerFileObj(editId, uploadedFiles);
        }
        return file;
      });
      // console.log({ newAnswer });
    } else {
      const newFile = buildAnswerFileObj(answerFiles.length + 1, uploadedFiles);
      //Document-validationa api integration---- start --------------
      if (formField.interfaceOptions.hasOwnProperty("rara") && companyId) {
        let validationApiResponse: any;
        setLoading(true);
        const data = JSON.stringify({
          document_url: uploadedFiles[0].path,
          //document_name: uploadedFiles[0].name,
          document_name: documentName,
          company_name: raraCompanyName,
          parentCompanies: [],
          isvaliddate: isValidDate,
        });
        var config = {
          headers: {
            Authorization: "Bearer " + getTokendetails.token,
            "Content-Type": "application/json",
          },
        };
        validationApiResponse = await axios
          .post(
            getTokendetails.serviceURL + "warp/document-validation",
            data,
            config
          )
          .then((res) => {
            return res;
          })
          .catch((err) => {
            return err;
          });
        console.log({ "validationApiResponse Outer": validationApiResponse });
        if (
          !!validationApiResponse?.error ||
          validationApiResponse?.data === ""
        ) {
          console.log({ "validationApiResponse 220": validationApiResponse });
          // Handle error response
          setLoading(false);
          setfileerror(
            "Please upload a PDF file. Invalid file format detected."
          );
          setTimeout(() => {
            setfileerror("");
            clearFields();
          }, 7000);
          newAnswer = [...answerFiles];
          state.setValue(formField.id, newAnswer);
        } else {
          console.log({ "validationApiResponse 233": validationApiResponse });
          setLoading(false);
          if (
            validationApiResponse?.data?.valid?.toString()?.toLowerCase() ===
            "false"
          ) {
            // In case of validation failure
            // setValidationMessage(validationApiResponse.data.explanation);
            // postParentMessage(raraAlertPopup(validationApiResponse?.data?.explanation));
            try {
              let currentDate = new Date();
              newFile.value[0].explanation =
                validationApiResponse?.data?.explanation;
              newFile.value[0].fileId = currentDate.getTime();
              newFile.value[0].validation = false;
              newAnswer = [...answerFiles, newFile];
              let warningListdata = cloneDeep(
                useWarningMessageStore.getState().WarningRuleFields
              );
              warningListdata.push({
                isWarningRule: true,
                formfieldid: formField.id,
                ispopupmessageremoved: false,
                questionid: formField.Question?.id,
                warningmessage: validationApiResponse?.data?.explanation,
                isFileUpload: true,
                fileId: newFile.value[0].fileId.toString(),
              });
              useWarningMessageStore.setState({
                WarningRuleFields: warningListdata,
              });
              state.setValue(formField.id, newAnswer);
              clearFields();
            } catch (error) {
              console.log({ error: error });
            }
          } else {
            // In case of validation success
            let currentDate = new Date();
            newFile.value[0].fileId = currentDate.getTime();
            newAnswer = [...answerFiles, newFile];
            state.setValue(formField.id, newAnswer);
            clearFields();
          }
        }
      }
      //end-------------------------------------------
      else {
        newAnswer = [...answerFiles, newFile];
        state.setValue(formField.id, newAnswer);
        clearFields();
      }
    }
  };

  const editHandler = (file: any) => () => {
    // console.log(file);

    if (!file) return;

    setEditId(file._id);
    const { value, _id, ...rest } = file;
    if (value) setUploadedFiles(value);
    if (rest) {
      formField.children?.forEach((childField: any) =>
        setChildFieldState(formField, childField, rest[childField.field].value)
      );
    }
  };

  const removeFileItemFromList = (file: any) => () => {
    if (!answerFiles.length) return;
    const newValue = answerFiles.filter((m) => m !== file);
    state.setValue(formField.id, newValue);
    let fileId = file.value[0]?.fileId ?? file?.fileId;
    if (!!file) {
      let removeSource = clickedFileIds;
      file?.value.forEach((fileItem: any) => {
        removeSource = clickedFileIds.filter(
          (items) => items?.filepath !== fileItem.path
        );
      });
      setClickedFileIds(removeSource);
    }
    if (!!fileId) {
      let warningList = useWarningMessageStore.getState().WarningRuleFields;
      let warningArray = warningList.filter(
        (z) => z.formfieldid !== formField.id && z.fileId !== fileId
      );
      let isEmpty = warningArray.length == 0 ? true : false;
      if (isEmpty) {
        removeWarning();
      } else {
        useWarningMessageStore.setState({
          WarningRuleFields: warningArray,
        });
      }
    }
  };

  const accept = Array.isArray(state.interfaceOptions?.accept)
    ? state.interfaceOptions?.accept.join(",")
    : undefined;
  const displayFormats: string[] = [];
  if (Array.isArray(state.interfaceOptions?.accept)) {
    state.interfaceOptions?.accept.forEach((items) => {
      if (items.includes(",")) {
        items.split(",").forEach((commaItem) => {
          const extension = Object.entries(MIME_TYPES).filter(
            ([key, value]) => value == commaItem.trim()
          );
          if (extension.length > 0) {
            displayFormats.push("." + extension[0][0]);
          }
        });
      } else {
        const extension = Object.entries(MIME_TYPES).filter(
          ([key, value]) => value == items.trim()
        );
        if (extension.length > 0) {
          displayFormats.push("." + extension[0][0]);
        }
      }
    });
  }
  const acceptedFormats =
    displayFormats.length > 0 ? displayFormats.join(", ") : undefined;

  useFormFieldRemoveAnswerOnEnableFalse(formField, state.fieldOptions.enable);
  if (!state.fieldOptions.enable) return <></>;
  // console.log("render", "File", formField.field);
  let fileerrors: any = "";

  let recommedationData: any = [];
  let isFormSubmitted = useFormFieldStore.getState().isFormSubmitted;
  let isCarryForward = useFormFieldStore.getState().isCarryForward;
  const isViewMode = query?.mode === FormMode.View;
  const isViewRecommendation = query?.mode === FormMode.ViewRecommendation;
  if (
    isViewMode &&
    formField?.recommendationCalc?.recommendation !== undefined &&
    isFormSubmitted
  ) {
    const StoreAnswer: any = useFormFieldStore.getState().answer;
    recommedationData = jsonata(
      formField?.recommendationCalc?.recommendation
    ).evaluate(StoreAnswer);
  }
  let EnableQuestionField = false;
  if (
    userSession?.user?.role === AppRoles.Invitee ||
    userSession?.user?.role === AppRoles.Responder
  ) {
    let getInterimRecomendation: any =
      useInterimAnswerStore.getState().interim_answers;
    let getSpecificRecomm = getInterimRecomendation[formField.field];

    let chkIsAnyOpen = false;
    if (getSpecificRecomm?.interim_recommendation?.length > 0) {
      let chkIsAnyOpenLength =
        getSpecificRecomm?.interim_recommendation?.filter(
          (x: any) =>
            x.status !== RecommendationStatus.Closed &&
            x.status !== RecommendationStatus.PendingForApproval
        );
      if (chkIsAnyOpenLength?.length > 0) {
        chkIsAnyOpen = true;
      }
    } else if (
      !!getSpecificRecomm?.interim_answer?.Interim_Recommendations &&
      getSpecificRecomm?.interim_answer?.Interim_Recommendations?.length > 0
    ) {
      let chkIsAnyOpenLength =
        getSpecificRecomm?.interim_answer?.Interim_Recommendations?.filter(
          (x: any) =>
            x.status !== RecommendationStatus.Closed &&
            x.status !== RecommendationStatus.PendingForApproval
        );
      if (chkIsAnyOpenLength?.length > 0) {
        chkIsAnyOpen = true;
      }
    }

    EnableQuestionField =
      !getSpecificRecomm?.isViewOnly &&
      query?.mode === FormMode.ViewRecommendation &&
      formField.id === getSpecificRecomm?.formFieldId &&
      chkIsAnyOpen
        ? true
        : false;
  }
  // console.log("render", "File", formField.field);
  const showassigner =
    formField?.groupField?.indexOf("tabs") > -1 ? "Show" : "";

  const RatingTable = ({ value }: { value: any }) => {
    let data = convertKeysToLowerCase(value);
    // if (data?.error) {
    //   return <p>{data?.error}</p>
    // }
    let recommendationsLength = 0;
    if (data && data["recommendations"]) {
      recommendationsLength = data["recommendations"].length;
    }
    let rating =
      Object.keys(data)
        .filter((z) => typeof data[z] === "number")
        .flatMap((v) => {
          if (typeof data[v] === "number") {
            return data[v];
          }
        }).length > 0
        ? Object.keys(data)
            .filter((z) => typeof data[z] === "number")
            .flatMap((v) => {
              if (typeof data[v] === "number") {
                return data[v];
              }
            })[0]
        : "";
    let reason =
      Object.keys(data)
        .filter((z) => typeof data[z] === "string" && z.includes("reason"))
        .flatMap((v) => data[v]).length > 0
        ? Object.keys(data)
            .filter((z) => typeof data[z] === "string" && z.includes("reason"))
            .flatMap((v) => data[v])[0]
        : "";
    if (rating != "" || reason != "") {
      return (
        <Box bg={"#F1F1F1"} p={15}>
          <Group spacing={0}>
            <Text size={12} fw={700} color={"#666"}>
              Overall Rating:&nbsp;&nbsp;
              <Text fw={400} component="span">
                {rating + "/5"}
              </Text>
            </Text>
            {/* <Tooltip
              multiline
              w={450}
              arrowOffset={10}
              offset={-3}
              arrowSize={4}
              label={reason}
              position="bottom-start"
              withArrow
            >
              <ActionIcon
                p={0}
                style={{ pointerEvents: "all" }}
                variant="transparent"
              >
                <InfoIcon />
              </ActionIcon>
            </Tooltip> */}
          </Group>
          <Text size={12} fw={700} mt={15} color={"#666"}>
            Reason for Rating &nbsp;<i>(AI Generated)</i> :
          </Text>
          <Text size={12} color="#666">
            {reason}
          </Text>
          {recommendationsLength !== 0 ? (
            <>
              <Text size={12} fw={700} mt={15} color={"#666"}>
                Recommendations / What&#39;s Missing &nbsp;<i>(AI Generated)</i>{" "}
                :
              </Text>
              <ul
                style={{
                  fontSize: 12,
                  paddingLeft: 20,
                  color: "#666",
                  margin: 0,
                }}
              >
                {data["recommendations"]?.map((row: any) => {
                  return <li>{row}</li>;
                })}
              </ul>{" "}
            </>
          ) : (
            <></>
          )}
        </Box>
      );
    } else {
      return <p>{"Invalid Document. Please delete and re-upload the file"}</p>;
    }
  };
  function convertKeysToLowerCase(obj: any): any {
    const newObj: any = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        newObj[key.toLowerCase()] = obj[key];
      }
    }
    return newObj;
  }
  //#region Carousel Data Binding
  let AISuggestionCarouseldata: AICArouselData[] = [];
  if (!!pageLoadSourceFileData && pageLoadSourceFileData?.Sources.length > 0) {
    AISuggestionCarouseldata = getAISuggestionCarouselforFileData(
      pageLoadSourceFileData?.Sources,
      formField?.id
    );
  }
  //#endregion
  const onFileChange = async (
    id: string,
    value: any,
    fileUrlDetails: Record<string, any>[],
    isFromAI: boolean,
    isFromPopUp: boolean,
    selectedData?: multipleFileUploadClick[]
  ) => {
    let fileDetails: any = value;
    if (isFromAI) {
      if (fileUrlDetails.length > 0) {
        fileDetails = await urlToFile(
          fileUrlDetails[0].fileurl,
          fileUrlDetails[0].fileName
        );
        setTemporaryFiles([
          { sourceId: id, filepath: fileUrlDetails[0].fileurl },
        ]);
      } else {
        if (isFromPopUp) {
          const fileData = uploadedFiles.filter((items) =>
            selectedData?.some((item) => item.filepath == items.path)
          );
          const answerfileData = answerFiles.filter((items) =>
            selectedData?.some((item) => item.filepath == items.value[0].path)
          );
          state.setValue(formField.id, answerfileData);
          setUploadedFiles(fileData);
          setClickedFileIds(selectedData as multipleFileUploadClick[]);
        } else {
          const sourceId = clickedFileIds.filter(
            (items) => items.sourceId == id
          );
          if (sourceId.length > 0) {
            const removeStateData = answerFiles.filter(
              (items) => items.value[0].path != sourceId[0].filepath
            );
            state.setValue(formField.id, removeStateData);
            setUploadedFiles(
              uploadedFiles.filter(
                (items) => items.path != sourceId[0].filepath
              )
            );
            setClickedFileIds(
              clickedFileIds.filter((items) => items.sourceId != id)
            );
          }
        }
      }
    }
    const validateFile = accept?.includes(fileDetails?.type);
    if (!validateFile && !!fileDetails) {
      const fileerrors =
        !!accept && !!acceptedFormats
          ? "Invalid file type. Accepted formats are : " + acceptedFormats
          : "Invalid file type. Please upload a valid file type";
      setfileerror(fileerrors);
      return;
    } else if (!!fileDetails && fileDetails?.type === "") {
      const fileerrors =
        !!accept && !!acceptedFormats
          ? "Invalid file type. Accepted formats are : " + acceptedFormats
          : "Invalid file type. Please upload a valid file type";
      setfileerror(fileerrors);
      return;
    }
    // if (!!fileDetails) uploadFiles(fileDetails);
    fileerrors = FilevalidationErrorMessage(fileDetails);
    setfileerror(fileerrors);
    if (fileerrors === "") {
      if (!!fileDetails) uploadFiles(id, fileDetails);
    } else {
      return fileerrors;
    }
  };
  return (
    <Stack
      pos={"relative"}
      style={EnableQuestionField === true ? { pointerEvents: "all" } : {}}
    >
      <LoadingOverlay
        overlayColor={"#F7F9FB"}
        visible={loading}
        className="Loader-fileupload"
      />
      <DisplayLabel
        text={state.fieldOptions?.label}
        infoIconProps={state.interfaceOptions?.infoIconProps}
        subtitle={state.interfaceOptions?.subtitle}
        showassigner={showassigner}
        formField={formField}
      />
      <Group position="apart">
        <Group align={fileerror.length <= 0 ? "end" : "flex-start"}>
          <Stack
            spacing="sm"
            w={{ sm: 270, lg: 270, xl: 400 }}
            // sx={{ width: 400 }}
          >
            <>
              <FileInput
                ref={inputRef}
                classNames={{ input: "mantine-FileInput-input" }}
                pl={formField.interfaceOptions?.subtitle ? 30 : 0}
                placeholder={state.interfaceOptions?.placeholder}
                withAsterisk={state.fieldOptions?.required}
                disabled={state.fieldOptions?.readonly}
                multiple={false}
                accept={accept}
                rightSectionWidth={45}
                error={validationErrorMessage(
                  state.fieldOptions?.required,
                  answerFiles,
                  "array",
                  setErrorMessagedetails,
                  formField.validationRules ?? ""
                )}
                onChange={(value: File | null) => {
                  if (!value) return;
                  setFiles(null);

                  setTimeout(() => {
                    const validateFile =
                      value.type.length === 0
                        ? false
                        : accept?.includes(value.type);
                    if (!validateFile) {
                      const filerror =
                        !!accept && !!acceptedFormats
                          ? "Invalid file type. Accepted formats are : " +
                            acceptedFormats
                          : "Invalid file type. Please upload a valid file type";
                      setfileerror(filerror);
                      setUploadedFiles([]);
                      return;
                    }
                    // if (!!value) uploadFiles(value);
                    const fileErrors = FilevalidationErrorMessage(value);
                    setfileerror(fileErrors);
                    setUploadedFiles([]);

                    if (!fileErrors) {
                      uploadFiles("", value);
                      setFiles(value);
                    }
                  }, 0);
                }}
                value={files}
                rightSection={
                  !files && (
                    <UploadFileSvgIcon
                      width={30}
                      height={30}
                      fill="#ffffff"
                      onClick={() => inputRef.current?.click()}
                      style={{
                        cursor: "pointer",
                        pointerEvents: "auto",
                      }}
                    />
                  )
                }
                data-formfieldId={formField?.id}
                clearable
              />
              {fileerror.length <= 0 ? (
                ""
              ) : (
                <span
                  style={{ width: "150%", fontSize: "12px", color: "#FC4E4E" }}
                >
                  {fileerror}
                </span>
              )}
              {uploadedFiles.map((file: any) => (
                <>
                  {/* <Badge
                    w={{ xl: 350, lg: 275, sm: 275 }}
                    style={{
                      marginBottom: "8px",
                      textTransform: "none",
                      display: "flex",
                      justifyContent: "space-between",
                    }}
                    key={file.path}
                    variant="outline"
                    rightSection={
                      <ActionIcon
                        size="xs"
                        color="orange"
                        radius="xl"
                        variant="transparent"
                        onClick={() => {
                          const newValue = uploadedFiles.filter(
                            (_file: any) => _file.path !== file.path
                          );
                          setUploadedFiles(newValue);
                          const removeSource = clickedFileIds.filter(
                            (items) => items?.filepath !== file.path
                          );
                          setClickedFileIds(removeSource);
                        }}
                      >
                        <IconX size={10} />
                      </ActionIcon>
                    }
                    fullWidth={false}
                  >
                    <Anchor
                      style={{ position: "relative", zIndex: 9 }}
                      href={file.path}
                      target="_blank"
                      title={file.name}
                    >
                      {file.name}
                    </Anchor>
                  </Badge> */}
                  <Group noWrap spacing={2} position="apart">
                    <Group spacing={5} noWrap>
                      <SampleFileIcon />
                      <Anchor
                        style={{
                          position: "relative",
                          zIndex: 9,
                          whiteSpace: "nowrap",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          display: "inline-block",
                          color: "#444444",
                          fontSize: 14,
                          fontWeight: 400,
                          width: 200,
                        }}
                        href={file.path}
                        target="_blank"
                        title={file.name}
                        c={"#444444"}
                        fz={14}
                        fw={400}
                      >
                        {file.name}
                      </Anchor>
                    </Group>
                    {/* <ActionIcon
                      onClick={() => {
                        const newValue = uploadedFiles.filter(
                          (_file: any) => _file.path !== file.path
                        );
                        setUploadedFiles(newValue);

                        //Remove previously clicked file
                        const updatedClickedFileIds = clickedFileIds.filter(
                          (item) => item.filepath !== file.path
                        );
                        setClickedFileIds(updatedClickedFileIds);
                      }}
                    >
                      <DeleteTrashIcon />
                    </ActionIcon> */}
                    <ActionIcon
                      sx={{
                        background: "#005c81",
                        color: "#fff",
                        "&:hover": {
                          background: "#122F47",
                          color: "#fff",
                        },
                      }}
                      size="lg"
                      onClick={saveFiles}
                      disabled={!uploadedFiles.length}
                      title="Save and Add"
                    >
                      <IconPlus />
                    </ActionIcon>
                    <ActionIcon
                      sx={{
                        background: "#005c81",
                        color: "#fff",
                        "&:hover": {
                          background: "#122F47",
                          color: "#fff",
                        },
                      }}
                      size="lg"
                      disabled={!uploadedFiles.length}
                      onClick={() => {
                        const newValue = uploadedFiles.filter(
                          (_file: any) => _file.path !== file.path
                        );
                        setUploadedFiles(newValue);
                        //Remove previously clicked file
                        const updatedClickedFileIds = clickedFileIds.filter(
                          (item) => item.filepath !== file.path
                        );
                        setClickedFileIds(updatedClickedFileIds);
                        clearFields();
                      }}
                      title="Clear"
                    >
                      <IconX />
                    </ActionIcon>
                  </Group>
                </>
              ))}
            </>
            {FormHasRecommendation?.length === 0 &&
            recommedationData.length > 0 &&
            recommedationData.some((item: any) => item.value === files) ? (
              <CommonRecommendation
                recommText={
                  recommedationData.filter(
                    (item: any) => item.value === files
                  )[0].comment
                }
              />
            ) : (
              ""
            )}
          </Stack>
          <Group
            sx={{
              alignItems: "end",
              marginBottom:
                validationErrorMessage(
                  state.fieldOptions?.required,
                  answerFiles,
                  "array",
                  setErrorMessagedetails,
                  formField.validationRules ?? ""
                ) && !uploadedFiles.length
                  ? 17
                  : 0,
            }}
          >
            {formField.children?.map((childFormField) => (
              <FormFieldRender
                key={childFormField.id}
                formField={childFormField}
                name={getChildFieldStateName(formField, childFormField)}
                isSimple
              />
            ))}

            {/* <ActionIcon
              sx={{
                background: "#005c81",
                color: "#fff",
                "&:hover": {
                  background: "#122F47",
                  color: "#fff",
                },
              }}
              size="lg"
              onClick={saveFiles}
              disabled={!uploadedFiles.length}
              title="Save and Add"
            >
              <IconPlus />
            </ActionIcon>
            <ActionIcon
              sx={{
                background: "#005c81",
                color: "#fff",
                "&:hover": {
                  background: "#122F47",
                  color: "#fff",
                },
              }}
              size="lg"
              disabled={!uploadedFiles.length}
              onClick={clearFields}
              title="Clear"
            >
              <IconX />
            </ActionIcon> */}
          </Group>
        </Group>
        <AddRecommendationButton
          formField={formField}
          answerOptionData={selectedFile}
        />
      </Group>
      <Stack w={"93%"}>
        {AISuggestionCarouseldata?.length > 0 &&
        !state.fieldOptions?.readonly ? (
          <AISuggestionCarousel
            data={AISuggestionCarouseldata}
            onFileCard={(id, value, isFromPopUp, selectedData) => {
              onFileChange(
                id,
                null,
                value as Record<string, any>[],
                true,
                isFromPopUp,
                selectedData
              );
            }}
            formFieldId={formField?.id}
            isclicked={false}
            isFile={true}
            clickedFileIds={
              !!useFormFieldStore.getState().answer[formField?.field]
                ? blankCheck.includes(
                    useFormFieldStore.getState().answer[formField?.field].value
                  )
                  ? []
                  : clickedFileIds
                : clickedFileIds
            }
          />
        ) : (
          <></>
        )}
      </Stack>
      {!!answerFiles.length && (
        <Stack style={{ width: "93%" }} spacing="sm" mt={10}>
          <Text style={{ fontWeight: "500" }} size={16}>
            Uploaded files will be listed below :-
          </Text>
          <Divider />
          <CommonTable
            headers={[
              "Document",
              ...(formField.children?.map((m: any) => m.fieldOptions.label) ??
                []),
              !isViewMode ? "Actions" : "",
            ]}
            sortedChildrenData={[]}
          >
            {answerFiles.map((file: any, index: any) => (
              <tr
                key={index}
                style={{
                  backgroundColor: file?._id === editId ? "#FAF7F0" : "inherit",
                  wordBreak: "break-all",
                  pointerEvents: query.mode === "start" ? "all" : "none",
                }}
              >
                <td>
                  <Stack>
                    {file.value.map((m: any) => (
                      <Stack spacing={2}>
                        <Group my={7} spacing={2}>
                          {!!documentName && (
                            <Text size={12} color="#666" fw={700}>
                              {" "}
                              Uploaded File:&nbsp;&nbsp;
                            </Text>
                          )}
                          <Anchor
                            style={{
                              pointerEvents: "all",
                              color: !!documentName ? "#FF9907" : "#72D0C6",
                            }}
                            key={m.name}
                            href={m.path}
                            target="_blank"
                            size={12}
                            fw={700}
                          >
                            {m.name}
                          </Anchor>
                        </Group>
                        {m?.explanation && (
                          <>
                            <Text mb={10} size={12} color="#1C9689" fw={700}>
                              Observations &nbsp;<i>(AI Generated)</i>{" "}
                              :&nbsp;&nbsp;
                              <Text
                                lang="en"
                                component="span"
                                color="#1C9689"
                                fw={400}
                                size={12}
                                style={{
                                  wordBreak: "keep-all",
                                  overflowWrap: "break-word",
                                  hyphens: "auto",
                                }}
                              >
                                {m?.explanation}
                              </Text>
                            </Text>
                          </>
                        )}
                        {isCarryForward
                          ? currentRating &&
                            currentRating?.map((item: any) => {
                              //return <RatingTable value={item.data} />;
                              return m.fileId == item.fileId ? (
                                <RatingTable value={item.data} />
                              ) : (
                                ""
                              );
                            })
                          : (isViewRecommendation || isViewMode) &&
                            currentRating &&
                            currentRating?.map((item: any) => {
                              //return <RatingTable value={item.data} />;
                              return m.fileId == item.fileId ? (
                                <RatingTable value={item.data} />
                              ) : (
                                ""
                              );
                            })}
                      </Stack>
                    ))}
                  </Stack>
                </td>
                {formField.children?.map((childField: any) => (
                  <td key={childField.field}>
                    <Text size="sm">
                      {convertInterfaceValueToString(
                        childField.interface,
                        file[childField.field].value
                      )}
                    </Text>
                  </td>
                ))}
                {!isViewMode && (
                  <td
                    style={{
                      verticalAlign: "top",
                    }}
                  >
                    <Group spacing={5} mt={-3}>
                      {hasAdditinalData && (
                        <ActionIcon
                          disabled={!!uploadedFiles.length}
                          onClick={editHandler(file)}
                        >
                          <IconPencil size={18} />
                        </ActionIcon>
                      )}

                      <ActionIcon
                        disabled={!!uploadedFiles.length}
                        onClick={removeFileItemFromList(file)}
                      >
                        <IconTrash size={18} />
                      </ActionIcon>
                    </Group>
                  </td>
                )}
              </tr>
            ))}
          </CommonTable>
        </Stack>
      )}
    </Stack>
  );
};

const SimpleFileField: FormFieldControl<"file"> = ({
  formField,
  name,
  onChange,
  rowIndex,
  ansId,
}) => {
  const userSession = useUserSession();
  const { query } = useRouter();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  // const { data: recommendationNewResponce } = useGetGlobalMasterByTypeQuery({
  //   variables: { type: "Recommendation_new" },
  // });
  const recommendationNewResponce: any = userSession?.GlobalMaster?.filter(
    (x: any) => x.type === "Recommendation_new"
  );
  let FormHasRecommendation: any = recommendationNewResponce[0]?.data.filter(
    (rec: any) => rec.FormId === formField?.formId
  );
  const state = useChildFieldState(String(name), formField, rowIndex, ansId);
  const [loading, setLoading] = useState(false);
  const inputRef = useRef<HTMLButtonElement>(null);
  const { uploadFile } = useFromFileUpload();
  const [fileerror, setfileerror] = useState("");
  useEffect(() => {
    if (!!selectedFile) uploadFiles();
  }, [selectedFile]);

  if (!state?.fieldOptions.enable) return <></>;
  const changeHandler = (value: any) => {
    state?.setValue(value);
    !!onChange && onChange(value);
  };

  const uploadFiles = async () => {
    try {
      if (!selectedFile) return changeHandler([]);

      setLoading(true);

      const fileResult = await uploadFile(selectedFile, false, "");

      if (!!fileResult) changeHandler([fileResult]);
    } catch (error) {
      console.log("SimpleFileField", error);
    }

    setSelectedFile(null);
    setLoading(false);
  };

  const accept = Array.isArray(formField.interfaceOptions?.accept)
    ? formField.interfaceOptions?.accept.join(",")
    : undefined;

  let recommedationData: any = [];
  const isViewMode = query?.mode === FormMode.View;
  if (
    isViewMode &&
    formField?.recommendationCalc?.recommendation !== undefined
  ) {
    const StoreAnswer: any = useFormFieldStore.getState().answer;
    recommedationData = jsonata(
      formField?.recommendationCalc?.recommendation
    ).evaluate(StoreAnswer);
  }
  let fileerrors: any = "";
  let EnableQuestionField = false;
  if (
    userSession?.user?.role === AppRoles.Invitee ||
    userSession?.user?.role === AppRoles.Responder
  ) {
    let getInterimRecomendation: any =
      useInterimAnswerStore.getState().interim_answers;
    let getSpecificRecomm = getInterimRecomendation[formField.field];

    let chkIsAnyOpen = false;
    if (getSpecificRecomm?.interim_recommendation?.length > 0) {
      let chkIsAnyOpenLength =
        getSpecificRecomm?.interim_recommendation?.filter(
          (x: any) =>
            x.status !== RecommendationStatus.Closed &&
            x.status !== RecommendationStatus.PendingForApproval
        );
      if (chkIsAnyOpenLength?.length > 0) {
        chkIsAnyOpen = true;
      }
    } else if (
      !!getSpecificRecomm?.interim_answer?.Interim_Recommendations &&
      getSpecificRecomm?.interim_answer?.Interim_Recommendations?.length > 0
    ) {
      let chkIsAnyOpenLength =
        getSpecificRecomm?.interim_answer?.Interim_Recommendations?.filter(
          (x: any) =>
            x.status !== RecommendationStatus.Closed &&
            x.status !== RecommendationStatus.PendingForApproval
        );
      if (chkIsAnyOpenLength?.length > 0) {
        chkIsAnyOpen = true;
      }
    }

    EnableQuestionField =
      !getSpecificRecomm?.isViewOnly &&
      query?.mode === FormMode.ViewRecommendation &&
      formField.id === getSpecificRecomm?.formFieldId &&
      chkIsAnyOpen
        ? true
        : false;
  }
  const showassigner =
    formField?.groupField?.indexOf("tabs") > -1 ? "Show" : "";
  return (
    <Stack
      spacing="sm"
      pos={"relative"}
      style={EnableQuestionField === true ? { pointerEvents: "all" } : {}}
    >
      <LoadingOverlay overlayColor={"#F7F9FB"} visible={loading} />
      {!!formField.interfaceOptions?.showLabel && (
        <DisplayLabel
          text={formField.fieldOptions?.label}
          infoIconProps={formField.interfaceOptions?.infoIconProps}
          subtitle={formField.interfaceOptions?.subtitle}
          showassigner={showassigner}
        />
      )}
      {!state?.value?.length && (
        <>
          <FileInput
            classNames={{ input: "mantine-FileInput-input" }}
            pl={formField.interfaceOptions?.subtitle ? 30 : 0}
            ref={inputRef}
            placeholder={formField.interfaceOptions?.placeholder}
            withAsterisk={formField.fieldOptions?.required}
            disabled={loading || formField.fieldOptions?.readonly}
            multiple={false}
            accept={accept}
            rightSectionWidth={45}
            error={validationErrorMessage(
              formField.fieldOptions?.required,
              accept,
              "array",
              setErrorMessagedetails,
              formField.validationRules ?? ""
            )}
            onChange={(file: any) => {
              fileerrors = FilevalidationErrorMessage(file);
              setfileerror(fileerrors);
              if (fileerrors === "") {
                if (!file) return setSelectedFile(null);
                setSelectedFile(file);
              } else {
              }
            }}
            value={selectedFile}
            rightSection={
              <UploadFileSvgIcon width={30} height={30} fill="#ffffff" />
            }
            clearable
          />
          {fileerror.length <= 0 ? (
            ""
          ) : (
            <span style={{ fontSize: "12px", marginTop: "5px", color: "red" }}>
              {fileerror}
            </span>
          )}
        </>
      )}
      {FormHasRecommendation?.length === 0 &&
      recommedationData.length > 0 &&
      recommedationData.some((item: any) => item.value === selectedFile) ? (
        <CommonRecommendation
          recommText={
            recommedationData.filter(
              (item: any) => item.value === selectedFile
            )[0].comment
          }
        />
      ) : (
        ""
      )}
      {!!state?.value?.length && (
        <Stack spacing="xs" align="flex-start">
          {state.value.map((file: any) => (
            <Badge
              style={{ textTransform: "none" }}
              key={file.path}
              variant="dot"
              rightSection={
                <ActionIcon
                  size="xs"
                  color="orange"
                  radius="xl"
                  variant="transparent"
                  onClick={() => {
                    const newValue = state.value.filter(
                      (_file: any) => _file !== file
                    );
                    changeHandler(newValue);
                    state.setValue(newValue);
                  }}
                >
                  <IconX size={10} />
                </ActionIcon>
              }
              fullWidth={false}
            >
              <Anchor
                style={{ pointerEvents: "all" }}
                href={file.path}
                target="_blank"
              >
                {file.name}
              </Anchor>
            </Badge>
          ))}
        </Stack>
      )}
    </Stack>
  );
};

const FileFieldWrapper: FormFieldControl = (props) =>
  props.isSimple ? (
    <SimpleFileField {...(props as any)} />
  ) : (
    <FileField {...props} />
  );

export default memo(
  FileFieldWrapper,
  (prev, next) => !!prev.formField === !!next.formField
);
