import { Box, Stack } from "@mantine/core";
import WarpForm from "@warp/client/features/form";
import { useFormFieldStore } from "@warp/client/features/form/store";
import { GetRenderFormDetailsQuerySuggestionType } from "@warp/client/features/form/types";
import { useUserSession } from "@warp/client/hooks/use-user-session";
import {
  PageReload,
  warpShowHideCommentList,
} from "@warp/client/services/platform-window-message.service";
import { InvitationComment_Bool_Exp } from "@warp/graphql/generated/types";
import { useGetAnsweredResultLazyQuery } from "@warp/graphql/queries/generated/get-answered-result";
import { useGetAssignedQuestionDetailsByInvitationIdQuery } from "@warp/graphql/queries/generated/get-assigned-question-details-by-invitation-id";
import { useGetCompanyDetailByIdLazyQuery } from "@warp/graphql/queries/generated/get-companydetail-by-id";
import { useGetinvitationcommentQuery } from "@warp/graphql/queries/generated/get-invitation-comment";
import { useGetvalidationandratingQuery } from "@warp/graphql/queries/generated/get-rara-validation-and-rating";
import { useGetRecommendationRenderFormDetailsLazyQuery } from "@warp/graphql/queries/generated/get-recommendation-render-form-details";
import { useGetRenderFormDetailsLazyQuery } from "@warp/graphql/queries/generated/get-render-form-details";
import { useGetRenderFormFieldDetailsLazyQuery } from "@warp/graphql/queries/generated/get-render-formfield-details";
import {
  AppRoles,
  FormInvitationStatus,
  FormMode,
  userInvitationAIStatus,
} from "@warp/shared/constants/app.constants";
import jwt from "jsonwebtoken";
import { useRouter } from "next/router";
import { FC, memo, useEffect, useMemo, useState } from "react";
const WarpFormScreen: FC<{
  invitationId: string;
}> = ({ invitationId }) => {
  const assignedQuestionDetail =
    useGetAssignedQuestionDetailsByInvitationIdQuery({
      variables: { invitationId: invitationId },
    });
  const postParentMessage = (message: string) =>
    typeof window !== "undefined" && window?.parent?.postMessage(message, "*");
  const { query } = useRouter();
  const isViewMode = query?.mode === FormMode.View;
  const userSession = useUserSession();
  const chkStore = useFormFieldStore((store) => store);
  const [formDetails, setformDetails] = useState<any>();
  const [formFieldsDetails, setformFieldsDetails] = useState<any>();
  const [loading, setLoading] = useState<boolean>();
  const [hasFetchedCompanyDetails, setHasFetchedCompanyDetails] =
    useState(false);
  const fetchData = useGetRenderFormDetailsLazyQuery()[0];
  const fetchFormFieldData = useGetRenderFormFieldDetailsLazyQuery()[0];
  const fetchRecommendationData =
    useGetRecommendationRenderFormDetailsLazyQuery()[0];
  let invitaionIdArray: any = [];
  invitaionIdArray.push(invitationId);
  const { data: ratingValidation } = useGetvalidationandratingQuery({
    variables: {
      invitationId: invitaionIdArray,
    },
  });
  // showing form result in console for View mode only
  const [answeredQuestionResult, { data: answeredData }] =
    useGetAnsweredResultLazyQuery({
      variables: { invitationId },
    });
  let isCarryforward: boolean = false;
  let isAIDataPointsAdded: boolean = false;
  const [fetchCompanyDetails, { data: companyDetails }] =
    useGetCompanyDetailByIdLazyQuery({
      variables: {
        id: userSession?.company?.id,
      },
    });
  let wheredata: InvitationComment_Bool_Exp = {
    invitationId: { _eq: invitationId },
    isActive: { _eq: true },
  };
  const invitationCommentsListData = useGetinvitationcommentQuery({
    variables: {
      where: wheredata,
    },
  });
  // let distinctquestion: any = [];
  let totalCommentsOnQuestions: number = 0;
  let totalCommentButton: number = 0;
  if (formFieldsDetails?.FormInvitation[0]?.Form?.FormFields != undefined) {
    totalCommentButton =
      formFieldsDetails?.FormInvitation[0]?.Form?.FormFields.filter(
        (x: any) => x.interfaceOptions?.isAddcomment == true
      )?.length;
  }
  let isAllowAssignQuestion: boolean = false;
  if (formDetails?.FormInvitation[0]?.Form?.isDelegateQuestion != null) {
    isAllowAssignQuestion =
      formDetails?.FormInvitation[0]?.Form?.isDelegateQuestion;
  }
  if (invitationCommentsListData?.data?.InvitationComment != undefined) {
    if (userSession?.user?.role === AppRoles.Responder) {
      let alldata = assignedQuestionDetail?.data?.AssesseeUserMapping?.filter(
        (x: any) => x?.userByUserid?.id == userSession?.user?.id
      );
      alldata?.map((items: any) => {
        items?.Question?.FormFields.map((fielditem: any) => {
          if (
            invitationCommentsListData?.data?.InvitationComment != undefined
          ) {
            totalCommentsOnQuestions =
              totalCommentsOnQuestions +
              invitationCommentsListData?.data?.InvitationComment.filter(
                (d: any) => d.formFieldId == fielditem?.id
              ).length;
          }
        });
      });
    } else {
      totalCommentsOnQuestions =
        invitationCommentsListData?.data?.InvitationComment.filter(
          (d: any) => d.formFieldId != null
        ).length;
    }
  }

  if (isViewMode) console.log("QA-FormScore", answeredData); // Do not remove this.

  useEffect(() => {
    if (invitationId && chkStore?.formFields.length == 0) {
      if (query?.mode === FormMode.ViewRecommendation) {
        fetchRecommendationData({ variables: { invitationId } }).then(
          (response) => {
            setformDetails(response.data);
          }
        );

        fetchFormFieldData({ variables: { invitationId } }).then(
          (responseFormField) => {
            const { data: formFieldsData } = responseFormField;
            setformFieldsDetails(formFieldsData);
          }
        );
      } else {
        fetchData({
          variables: {
            invitationId,
          },
        }).then((response) => {
          setformDetails(response.data);
        });

        fetchFormFieldData({ variables: { invitationId } }).then(
          (responseFormField) => {
            const { data: formFieldsData } = responseFormField;
            setformFieldsDetails(formFieldsData);
          }
        );
      }
    }
    if (isViewMode) answeredQuestionResult();
  }, [
    invitationId,
    fetchData,
    fetchRecommendationData,
    answeredQuestionResult,
    isViewMode,
    loading,
    formDetails,
    formFieldsDetails,
  ]);

  let formDetails_new: any = {};
  const UserDetails = formDetails?.AssesseeUserMapping.filter(
    (item: any) => item.userId === userSession?.user?.id
  );
  if (!!formDetails && formDetails?.FormInvitation.length > 0) {
    isAIDataPointsAdded =
      formDetails?.FormInvitation[0]?.Form?.isAIDataPointsAdded;
  }
  let formInvitation: any;
  if (UserDetails !== undefined && UserDetails?.length > 0) {
    let unique = formFieldsDetails?.FormInvitation[0]?.Form?.FormFields.reduce(
      (acc: any, curr: any) => {
        const data = UserDetails?.find(
          (item: any) =>
            item.questionId === curr?.Question?.id || curr.Question === null
        );
        if (data) {
          acc.push(curr);
        }
        return acc;
      },
      []
    );

    let formFieldsList =
      formFieldsDetails?.FormInvitation[0]?.Form?.FormFields?.filter(
        (item: any) => {
          return UserDetails?.find(
            (itm: any) => itm.questionId === item.Question?.id
          );
        }
      );
    let wizardList: any = unique?.filter(
      (x: any) => x.interface === "group-wizard"
    );
    let wizardStepsList: any =
      formFieldsDetails?.FormInvitation[0]?.Form?.FormFields?.filter(
        (x: any) => x.interface === "group-wizard-step"
      );

    let wizardTabList: any =
      formFieldsDetails?.FormInvitation[0]?.Form?.FormFields?.filter(
        (x: any) => x.interface === "group-tabs"
      );
    let finalWizardList: any = formFieldsList?.filter(
      (y: any) => y.interface === "group-wizard"
    );
    if (finalWizardList?.length === 0) {
      formFieldsList?.push(wizardList[0]);
    }
    let getTabStep: any = [];
    let getTabStep1 = formFieldsList?.filter(
      (x: any) =>
        x.subtheme !== null || x.Section?.content === "Business Overview"
    );
    if (!!getTabStep1 && getTabStep1?.length > 0) {
      getTabStep = getTabStep1;
    } else {
      getTabStep = formFieldsList?.filter(
        (x: any) => x.interface != "group-wizard"
        //x.Section?.content === "PS1" || x.Section?.content === "PS2"
      );
    }
    getTabStep?.map((subDetail: any) => {
      let groupFieldList: any = unique?.filter(
        (x: any) => x.field === subDetail.groupField
      );
      let finalgroupFieldList: any = formFieldsList?.filter(
        (y: any) => y.field === groupFieldList[0]?.field
      );
      if (finalgroupFieldList?.length === 0) {
        formFieldsList?.push(groupFieldList[0]);
        let stepList: any = unique?.filter(
          (x: any) => x.field === groupFieldList[0]?.groupField
        );
        let filterStepList: any = formFieldsList?.filter(
          (y: any) => y.field === stepList[0]?.field
        );
        if (filterStepList?.length === 0) {
          formFieldsList?.push(stepList[0]);
          let getAllThemes = unique?.filter((x: any) => x.type === "sub-theme");

          let getSectionQuestion = getTabStep?.filter(
            (x: any) => x.Section?.parentSectionId === stepList[0]?.Section?.id
          );

          getSectionQuestion?.map((subTheme: any) => {
            let getSpecificThemes = getAllThemes?.filter(
              (x: any) =>
                x.subtheme === subTheme?.subtheme &&
                x.type === "sub-theme" &&
                x.Section?.parentSectionId === stepList[0]?.Section?.id &&
                x.Section?.content === subTheme.Section.content
            );

            let getSpecificThemesExist = formFieldsList?.filter(
              (x: any) =>
                x.subtheme === subTheme?.subtheme &&
                x.type === "sub-theme" &&
                x.Section?.parentSectionId === stepList[0]?.Section?.id &&
                x.Section?.content === subTheme.Section.content
            );
            if (
              getSpecificThemesExist?.length === 0 &&
              getSpecificThemes?.length > 0
            ) {
              formFieldsList?.push(getSpecificThemes[0]);

              let getSubThemeBreadCrumb =
                getSpecificThemes[0]?.interfaceOptions?.breadcrumb;
              getSubThemeBreadCrumb
                ?.split(">")
                .map((item: any, index: number) => {
                  let getSpecificMainThemes = getAllThemes?.filter(
                    (x: any) =>
                      x.field === getSpecificThemes[0]?.groupField &&
                      x.type === "sub-theme" &&
                      x.subtheme === item.trim()
                  );

                  let getSpecificMainThemesExist = formFieldsList?.filter(
                    (x: any) =>
                      x.field === getSpecificThemes[0]?.groupField &&
                      x.type === "sub-theme" &&
                      x.subtheme === item.trim()
                  );
                  if (
                    getSpecificMainThemesExist?.length === 0 &&
                    getSpecificMainThemes?.length > 0
                  ) {
                    formFieldsList?.push(getSpecificMainThemes[0]);
                  }
                });
            }
          });
        }
      }
    });

    if (getTabStep?.length === 0) {
      wizardStepsList.map((item: any) => formFieldsList?.push(item));
      wizardTabList.map((item: any) => formFieldsList?.push(item));
    }
    formDetails_new = {
      AssesseeUserMapping: formDetails?.AssesseeUserMapping,
      FormInvitation: [
        {
          Form: {
            Details: formDetails?.FormInvitation[0]?.Form?.Details,
            FormFields: formFieldsList,
            id: formDetails?.FormInvitation[0]?.Form?.id,
            name: formDetails?.FormInvitation[0]?.Form?.name,
            __typename: formDetails?.FormInvitation[0]?.Form.__typename,
          },
          FormSubmissions: formDetails?.FormInvitation[0]?.FormSubmissions,
          ParentCompanyMapping:
            formDetails?.FormInvitation[0]?.ParentCompanyMapping,
          id: formDetails?.FormInvitation[0]?.id,
          status: formDetails?.FormInvitation[0]?.status,
          __typename: formDetails?.FormInvitation[0]?.__typename,
        },
      ],
    };

    formInvitation = formDetails_new?.FormInvitation[0];
  } else {
    formInvitation = formDetails?.FormInvitation[0];
  }

  //const formInvitation = formDetails?.FormInvitation[0];
  let formSubmission: any;
  let formFields: any;
  let answers: any;
  let formId: any;
  let isError = false;
  if (
    !!formInvitation?.FormSubmissions[0]?.IsCarryForward &&
    formInvitation?.FormSubmissions[0]?.IsCarryForward.length > 0
  ) {
    if (
      formInvitation?.FormSubmissions[0]?.IsCarryForward?.filter(
        (d: any) => d.Interim_Answers.length > 0
      ).length > 0
    ) {
      isCarryforward = true;
    } else {
      if (
        !!formInvitation?.FormSubmissions[0]?.Interim_Answers &&
        formInvitation?.FormSubmissions[0]?.Interim_Answers.length > 0
      ) {
        if (
          formInvitation?.FormSubmissions[0]?.Interim_Answers?.filter(
            (d: any) => !!d.Interim_Answer && d.Interim_Answer.length > 0
          ).length > 0
        ) {
          isCarryforward = true;
        }
      }
    }
  }
  if (query?.mode === FormMode.Start) {
    if (
      (userSession?.user?.role === AppRoles.Invitee ||
        userSession?.user?.role === AppRoles.Responder) &&
      formInvitation?.status === FormInvitationStatus.Draft
    ) {
      formSubmission = formInvitation?.FormSubmissions[0];
      formFields =
        formInvitation?.Form?.FormFields?.length > 0
          ? formInvitation?.Form?.FormFields
          : formFieldsDetails?.FormInvitation[0]?.Form?.FormFields;
      answers = formSubmission?.Answers;
      formId = formInvitation?.Form?.id;
      isError = false;
    } else if (
      (userSession?.user?.role === AppRoles.Invitee ||
        userSession?.user?.role === AppRoles.Responder) &&
      formInvitation?.status === FormInvitationStatus.Invited
    ) {
      formSubmission = formInvitation?.FormSubmissions[0];
      formFields =
        formInvitation?.Form?.FormFields?.length > 0
          ? formInvitation?.Form?.FormFields
          : formFieldsDetails?.FormInvitation[0]?.Form?.FormFields;
      answers = formSubmission?.Answers;
      formId = formInvitation?.Form?.id;
      isError = false;
    } else {
      isError = true;
    }
  } else if (query?.mode === FormMode.View) {
    formSubmission = formInvitation?.FormSubmissions[0];
    formFields =
      formInvitation?.Form?.FormFields?.length > 0
        ? formInvitation?.Form?.FormFields
        : formFieldsDetails?.FormInvitation[0]?.Form?.FormFields;
    answers = formSubmission?.Answers;
    formId = formInvitation?.Form?.id;
    isError = false;
  } else if (query?.mode === FormMode.ViewRecommendation) {
    formSubmission = formInvitation?.FormSubmissions[0];
    formFields =
      formInvitation?.Form?.FormFields?.length > 0
        ? formInvitation?.Form?.FormFields
        : formFieldsDetails?.FormInvitation[0]?.Form?.FormFields;
    answers = formSubmission?.Interim_Answers;
    formId = formInvitation?.Form?.id;
    isError = false;
  } else {
    isError = true;
  }
  let formFieldSuggestion: GetRenderFormDetailsQuerySuggestionType = [];
  if (
    userSession?.user?.role === AppRoles.Invitee ||
    userSession?.user?.role === AppRoles.Responder
  ) {
    formFieldSuggestion = formFieldsDetails?.FormInvitation[0]?.Suggestions;
  }
  let isFormSubmitted =
    formInvitation?.status === FormInvitationStatus.Submitted ||
    formInvitation?.status === FormInvitationStatus.Approved
      ? true
      : false;

  const commentsAccessData: any = userSession?.GlobalMaster?.filter(
    (x: any) => x.type === "CommentsAccess"
  );

  // const { data: commentsAccessData } = useGetGlobalMasterByTypeQuery({
  //   variables: {
  //     type: "CommentsAccess",
  //   },
  // });

  // const invitationListData = useGetinvitationcommentQuery({
  //   variables: {
  //     invitationId: invitationId,
  //   },
  // });

  formId = formDetails?.FormInvitation[0]?.Form?.id;

  const companyId = useMemo(() => {
    if (userSession?.user?.role === AppRoles.Consultant) {
      return userSession?.company?.id;
    }
    if (userSession?.user?.role === AppRoles.Invitee) {
      return formInvitation?.ParentCompanyMapping?.ParentCompanyId;
    }
    return userSession?.company?.id;
  }, [
    userSession?.user?.role,
    formInvitation?.ParentCompanyMapping?.ParentCompanyId,
    userSession?.company?.id,
  ]);

  const commentsAccessFilteredData = useMemo(() => {
    if (commentsAccessData && commentsAccessData.length > 0) {
      return commentsAccessData[0]?.data?.filter(
        (d: any) => d?.companyId === companyId && d?.formId === formId
      );
    }
    return [];
  }, [commentsAccessData, companyId, formId]);

  useEffect(() => {
    if (
      userSession?.user?.role === AppRoles.Consultant &&
      !hasFetchedCompanyDetails
    ) {
      fetchCompanyDetails();
      setHasFetchedCompanyDetails(true);
    }

    let variables = "";

    let IsShow: boolean = false;
    let IsShowComment: boolean = false;
    let IsEditComment: boolean = false;

    if (commentsAccessFilteredData.length > 0) {
      if (
        userSession?.user?.role === AppRoles.Inviter ||
        userSession?.user?.role === AppRoles.Consultant
      ) {
        switch (formInvitation?.status) {
          case FormInvitationStatus.Draft: {
            IsShow = false;
            IsShowComment = false;
            IsEditComment = true;
            break;
          }

          case FormInvitationStatus.Submitted: {
            if (!!commentsAccessFilteredData[0]?.IsReopeningEnabled) {
              IsShow = true;
              IsShowComment = false;
              IsEditComment = false;
              break;
            }
          }

          case FormInvitationStatus.Approved: {
            IsShow = false;
            IsShowComment = true;
            IsEditComment = false;
            break;
          }

          default:
            break;
        }
      } else if (
        userSession?.user?.role === AppRoles.Invitee ||
        userSession?.user?.role === AppRoles.Responder
      ) {
        switch (formInvitation?.status) {
          case FormInvitationStatus.Draft: {
            IsShow = false;
            IsShowComment = false;
            IsEditComment = true;
            break;
          }

          case FormInvitationStatus.Submitted: {
            IsShow = false;
            IsShowComment = true;
            break;
          }

          case FormInvitationStatus.Approved: {
            IsShow = false;
            IsShowComment = true;
            IsEditComment = false;
            break;
          }

          default:
            break;
        }
      }
    }
    variables = warpShowHideCommentList(
      IsShow,
      IsShowComment,
      IsEditComment,
      totalCommentsOnQuestions,
      totalCommentButton,
      isAllowAssignQuestion
    );
    postParentMessage(variables);
  }, [
    commentsAccessFilteredData,
    formInvitation?.status,
    userSession?.user?.role,
    hasFetchedCompanyDetails,
  ]);

  // if (loading || !formDetails) return <LoadingOverlay visible={true} />;
  if (loading || !formDetails) {
    return <></>;
  }

  if (isError && query?.mode === FormMode.Start) {
    postParentMessage(PageReload());
  }
  const decodedToken: any =
    typeof window !== "undefined"
      ? jwt.decode(String(userSession?.accessToken))
      : null;
  const AITokenValue = !!decodedToken
    ? decodedToken["https://hasura.io/jwt/claims"]["x-hasura-form-with-AI"]
    : null;
  const formAIDetailData: userInvitationAIStatus[] = !isCarryforward
    ? !!AITokenValue
      ? AITokenValue?.filter(
          (items: any) =>
            items?.formId == formId &&
            (items?.docWithAI == true || items?.onlyDoc == true)
        )
      : []
    : [];
  return (
    <Stack className="assessmentDetails-Ques-Ans">
      {isError === true ? (
        <Box>Access denied</Box>
      ) : (
        <WarpForm
          formFields={formFields as any}
          answers={answers}
          formId={formId}
          formInvitationId={String(invitationId)}
          formSubmissionId={formSubmission?.id}
          isFormSubmitted={isFormSubmitted}
          isCarryForward={isCarryforward}
          ratingValidation={ratingValidation}
          Suggestions={formAIDetailData.length > 0 ? formFieldSuggestion : []}
          isAIDataPointsAdded={isAIDataPointsAdded}
        />
      )}
    </Stack>
  );
};
export default memo(
  WarpFormScreen,
  (prev, next) => prev.invitationId === next.invitationId
);
