import { sdk } from "@warp/graphql/generated/server";
import {
  Answer_Insert_Input,
  AssessorConsultantMapping,
  BulkUpdateInterimRecommendationByInterimAnswerIdMutationVariables,
  Form,
  GetRenderFormFieldDetailsQuery,
  GetSourceDataByInvitationIdQuery,
  GlobalMaster,
  Interim_Answer_Insert_Input,
  Suggestions_Updates,
  UpdateAnswerMutationVariables,
  UpdateInterimAnswerByQuestionIdAndSubmissionIdMutationVariables,
  ValidationWarningLogs_Insert_Input,
} from "@warp/graphql/generated/types";
import {
  AICArouselData,
  AIDataCardsType,
  AppRoles,
  FormInvitationStatus,
  FormMode,
  getAIFeatureByDBType,
  InvitationIdAIDetails,
  Platform,
  RecommendationStatus,
  SourcesType,
  suggestionCategory,
  userInvitationAIStatus,
  webCurationAPIDataType,
} from "@warp/shared/constants/app.constants";
import jsonata from "jsonata";
import jwt from "jsonwebtoken";
import { useUserSession } from "../../hooks/use-user-session";
import {
  selectDisplayOptions,
  selectFieldOptions,
  selectInterfaceOptions,
  selectSimpleFieldOptions,
  useFormFieldStore,
  useWarningMessageStore,
} from "./store";
import {
  FormFieldWithChildrenType,
  GetRenderFormDetailsQuerySuggestionType,
  StateType,
} from "./types";
import { checkFormFieldIsInputType } from "./utils";
// eslint-disable-next-line react-hooks/rules-of-hooks
const userSession = useUserSession();

export const isUserAllowedAIFeature = (): boolean => {
  if (typeof window === "undefined") return false;

  const decodedToken: any = jwt.decode(String(userSession?.accessToken));
  if (!decodedToken) return false;

  const AITokenValue =
    decodedToken["https://hasura.io/jwt/claims"]?.["x-hasura-form-with-AI"];

  if (
    !AITokenValue ||
    !Array.isArray(AITokenValue) ||
    AITokenValue.length === 0
  )
    return false;

  return AITokenValue.some((item: any) => item?.docWithAI || item?.onlyDoc); //true: if any either of docWithAI/onlyDoc is true
};

const checkValidation = (validationRules: any, answer: any) => {
  const isAIUser = isUserAllowedAIFeature();

  let originalData = useFormFieldStore.getState().answer ?? answer;
  let safeAnswerData: any;

  if (isAIUser) {
    // Deep clone to prevent modifying the original state
    safeAnswerData = structuredClone(originalData);

    // Remove non-string/array values from the copy to avoid JSONata $length() errors
    if (validationRules.includes("replace")) {
      Object.keys(safeAnswerData).forEach((key) => {
        if (typeof safeAnswerData[key]?.value === "number") {
          safeAnswerData[key] = { value: String(safeAnswerData[key]?.value) };
        }
      });
    }
  }

  try {
    let Result = Boolean(
      jsonata(validationRules).evaluate(
        isAIUser ? safeAnswerData : originalData
      )
    );
    return Result;
  } catch (error) {
    console.error("JSONata evaluation error:", error);
    return false; // Return false if evaluation fails instead of crashing
  }
};

const isInputField = (field: StateType["formFields"][0]) => {
  if (field.interface.toLowerCase().includes("group")) return false;
  return true;
};

export const isAnswered = (
  formField: FormFieldWithChildrenType,
  _isAnswered: boolean = false,
  chkStore: any
) => {
  const _formFields =
    chkStore?.formFields?.length > 0
      ? chkStore?.formFields
          .filter((m: any) => m.Question?.id === formField.Question?.id)
          .filter(isInputField)
      : [];

  const formFieldKeys = _formFields.map((m: any) => m.field);
  const answerFormFieldKeys =
    chkStore?.answer && formFieldKeys
      ? Object.keys(chkStore.answer).filter((key) =>
          formFieldKeys.includes(key)
        )
      : [];

  const answersData = answerFormFieldKeys.reduce((result: any, curr, index) => {
    const ans = chkStore?.answer[curr];
    if (!!ans) result[curr] = ans;
    return result;
  }, {});

  if (formField.type === "label" || formField.type === "container") {
    _isAnswered = true;
  }

  const fieldOptions = selectFieldOptions(
    useFormFieldStore.getState(),
    formField.id
  );

  let children: any[] | undefined = [];

  if (
    formField.interface !== "select-multiple-dropdown" ||
    !formField.children?.length
  ) {
    children = formField.children
      ?.map((m: any) => {
        const fieldOptions = selectFieldOptions(
          useFormFieldStore.getState(),
          m.id
        );
        const displayOptions = selectDisplayOptions(
          useFormFieldStore.getState(),
          m.id
        );
        const interfaceOptions = selectInterfaceOptions(
          useFormFieldStore.getState(),
          m.id
        );

        return { ...m, fieldOptions, displayOptions, interfaceOptions };
      })
      .filter((m: any) => m.fieldOptions.enable);
  }

  // check interface value (input)
  if (checkFormFieldIsInputType(formField.interface as any)) {
    if (fieldOptions?.enable) {
      if (fieldOptions.required) {
        if (
          formField.interface === "select-multiple-dropdown" &&
          formField.children?.length
        ) {
          if (
            answersData[formField.field]?.value &&
            Array.isArray(answersData[formField.field]?.value) &&
            answersData[formField.field]?.value.length > 0
          ) {
            //Added condition to check value is an array or not, to resolve: [TypeError: ref9.map is not a function]
            _isAnswered = answersData[formField.field]?.value
              ?.map((rec: any, index: any) => {
                let _isAns = formField?.children
                  ?.map((m: any) => {
                    if (
                      Object.keys(rec).filter((a) => a === m?.field) &&
                      !!rec[m?.field]?.value
                    ) {
                      return true;
                    } else {
                      const fieldOptionsRow = selectSimpleFieldOptions(
                        useFormFieldStore.getState(),
                        m.id,
                        index
                      );
                      if (
                        fieldOptionsRow.enable === true &&
                        fieldOptionsRow.required === true
                      ) {
                        return false;
                      } else {
                        return true;
                      }
                    }
                  })
                  ?.every((v: any) => v === true);

                return _isAns;
              })
              ?.every((v: any) => v === true);
          } else {
            if (
              fieldOptions.enable === true &&
              fieldOptions.required === true
            ) {
              return false;
            } else {
              return true;
            }
          }
        } else if (
          formField.interface === "select-multiple-checkbox" &&
          formField.children?.length
        ) {
          if (answersData[formField.field]?.value?.length > 0) {
            _isAnswered = answersData[formField.field]?.value
              ?.map((rec: any, index: any) => {
                let _isAns = formField?.children
                  ?.map((m: any) => {
                    if (
                      Object.keys(rec).filter((a) => a === m?.field) &&
                      !!rec[m?.field]?.value
                    ) {
                      return true;
                    } else {
                      const fieldOptionsRow = selectSimpleFieldOptions(
                        useFormFieldStore.getState(),
                        m.id,
                        index
                      );
                      if (
                        fieldOptionsRow.enable === true &&
                        fieldOptionsRow.required === true
                      ) {
                        return false;
                      } else {
                        return true;
                      }
                    }
                  })
                  ?.every((v: any) => v === true);

                return _isAns;
              })
              ?.every((v: any) => v === true);
          } else {
            if (
              fieldOptions.enable === true &&
              fieldOptions.required === true
            ) {
              return false;
            } else {
              return true;
            }
          }
        } else if (
          formField.interface === "select-multiple-dropdown" &&
          !formField.children?.length
        ) {
          if (answersData[formField.field]?.value?.length > 0) {
            _isAnswered = true;
          } else {
            _isAnswered = false;
          }
        } else if (answersData[formField.field]?.value) {
          _isAnswered = true;
        } else {
          return false;
        }
      } else {
        // if parent is not required
        return true;
      }
    }
  }

  if (!!children?.length) {
    const allChildrenHasAnswers: any =
      children
        .map((child) => isAnswered(child, _isAnswered, chkStore))
        .filter((result) => !!result).length === children.length;

    _isAnswered = allChildrenHasAnswers;
  }

  if (!!formField.validationRules?.length) {
    if (Object.keys(answersData).length > 0) {
      formField.validationRules.map((m: any) => {
        _isAnswered = !checkValidation(m.rule, answersData);
        //   if (m.fieldOptions.enable) return true;
      });
    }
  }

  return _isAnswered;
};

export const warninglogsave: any = async (
  queryMode: string,
  invitationId: string,
  UpsertValidationWarningLogsMutation: any
) => {
  try {
    // const removeWarning = useWarningMessageStore(
    //   (store) => store.removeWarningRuleFields
    // );
    const WarningData1 = useWarningMessageStore.getState().WarningRuleFields;

    let InsertValidationWaringDeatils: ValidationWarningLogs_Insert_Input[] =
      [];
    let deletecondition: Record<string, any>[] = [];

    if (WarningData1.length > 0) {
      WarningData1.forEach((rec: any) => {
        let OldValueobject = { Value: rec?.oldvalue };
        let newValueobject = { Value: rec?.newValue };

        InsertValidationWaringDeatils.push({
          OldValue: OldValueobject,
          NewValue: newValueobject,
          Ratio: rec?.ratio,
          formFieldId: rec?.formfieldid,
          QuestionId: rec?.questionid,
          InvitationId: invitationId,
          created_by: userSession?.user?.id,
          updated_by: userSession?.user?.id,
          values: {
            OldValue: rec?.oldvalue,
            NewValue: rec?.newValue,
            ActualDeviation: rec?.ratio,
            Threshold_Deviation: rec?.Threshold_Deviation,
            Deviation_differential: rec?.Deviation_differential,
          },
          Logtype:
            queryMode === FormMode.ViewRecommendation
              ? "recommendation"
              : "invitation",
        });

        deletecondition.push({
          _and: {
            formFieldId: { _eq: rec?.formfieldid },
            InvitationId: { _eq: invitationId },
            Logtype: {
              _eq:
                queryMode === FormMode.ViewRecommendation
                  ? "recommendation"
                  : "invitation",
            },
          },
        });
      });
      useWarningMessageStore.setState({
        WarningRuleFields: [],
      });
      // removeWarning();
      const data = await UpsertValidationWarningLogsMutation({
        variables: {
          deleteobject: { _or: deletecondition },
          object: InsertValidationWaringDeatils,
        },
      });
    }
    useWarningMessageStore.setState({
      WarningRuleFields: [],
    });
    // removeWarning();

    return true;
  } catch (error) {
    console.error("Error in warninglogsave:", error);
  }
};
//// save Answer Start
export const setAnswersData = async (
  data: any,
  submissionId: any,
  Answerdata: any,
  OtherAnswerData: any,
  questionId: any,
  userId: any,
  storeData: any,
  formId: string,
  invitationId: string,
  pageName: string,
  recommendationStatus: string,
  formFieldId: string,
  getFormFieldsDetail: any,
  getFormfieldRecommendation: any,
  upsertAnswer: any,
  updateAnswer: any,
  updateInterimAnswer: any,
  insertInterimAnsweronUpdate: any,
  insertBulkAnswer: any,
  updateRecommendation: any,
  updateSuggestionData: any,
  suggestionStoreData: GetRenderFormDetailsQuerySuggestionType
) => {
  let oldAnswer: any = [];
  let updatedAnswer: any = [];
  let InsertInterimAnsweronUpdateData: any = [];
  let InsertAnswerOnUpdate: any = [];
  let isIntrimAnswer: boolean = false;

  let suggestionUpdate: Suggestions_Updates[] = [];
  if (
    !!data &&
    !!data?.Interim_Answer &&
    data?.Interim_Answer?.length > 0 &&
    data?.FormSubmission[0]?.FormInvitation?.status ==
      FormInvitationStatus.Submitted
  ) {
    oldAnswer = data?.Interim_Answer;
    // updatedAnswer = Answerdata.filter(
    //   (item1: any) =>
    //     !oldAnswer.some(
    //       (item2: any) =>
    //         item2.formFieldId === item1.formFieldId &&
    //         item2.data.value === item1.value
    //     )
    // );
    Answerdata.map((item: any) => {
      if (
        oldAnswer?.filter(
          (x: any) =>
            x.formFieldId === item.formFieldId && x.data?.value != item.value
        )
      ) {
        updatedAnswer.push(item);
      }
    });
    oldAnswer
      ?.filter(
        (s: any) =>
          Answerdata.filter((f: any) => f.formFieldId == s.formFieldId)
            .length == 0
      )
      .map((items: any) => {
        updatedAnswer.push(items);
      });
    Answerdata?.map((item1: any) => {
      if (
        oldAnswer?.filter(
          (item2: any) => item2.formFieldId === item1.formFieldId
        ).length == 0
      ) {
        InsertInterimAnsweronUpdateData.push(item1);
      }
    });
    isIntrimAnswer = true;
  } else {
    isIntrimAnswer = false;
    InsertAnswerOnUpdate = [];
    oldAnswer = data?.Answer;
    // updatedAnswer = OtherAnswerData.filter(
    //   (item1: any) =>
    //     !data?.Answer.some(
    //       (item2: any) =>
    //         item2.formFieldId === item1.formFieldId &&
    //         item2.data.value === item1.value
    //     )
    // );
    OtherAnswerData?.map((item: any) => {
      if (
        oldAnswer?.filter(
          (x: any) =>
            x.formFieldId === item.formFieldId && x.data?.value != item.value
        )
      ) {
        updatedAnswer.push(item);
      }
    });
    oldAnswer
      ?.filter(
        (s: any) =>
          OtherAnswerData?.filter((f: any) => f.formFieldId == s.formFieldId)
            .length == 0
      )
      .map((items: any) => {
        updatedAnswer.push(items);
      });

    Answerdata?.map((item1: any) => {
      if (
        data?.Answer?.filter(
          (item2: any) => item2.formFieldId === item1.formFieldId
        ).length == 0
      ) {
        InsertAnswerOnUpdate.push(item1);
      }
    });
  }
  let formFieldResponse: any = null;
  let formfieldids: any = null;
  let RecommendationsData: any = null;
  if (pageName !== "AddComment") {
    formFieldResponse = await getFormFieldsDetail({
      variables: {
        questionId: questionId,
      },
    });
    formfieldids = RecommendationsData = await getFormfieldRecommendation({
      variables: {
        submissionid: submissionId,
        formfieldid: formFieldResponse?.data?.FormField?.map(
          (item: any) => item.id
        ),
      },
    });
  }
  const currentSuggestion = suggestionStoreData.filter((idItem) =>
    formFieldResponse?.data?.FormField?.some(
      (item: any) => item.id == idItem.formFieldId
    )
  );
  currentSuggestion.forEach((suggestionItems, index) => {
    if ((index = 0)) {
      suggestionUpdate.push({
        where: {
          formFieldId: {
            _eq: suggestionItems?.formFieldId,
          },
          formInvitationId: {
            _eq: invitationId,
          },
        },
        _set: {
          isSelected: false,
          selectedByUserId: null,
        },
      });
    }
    suggestionUpdate.push({
      where: {
        id: {
          _eq: suggestionItems?.id,
        },
      },
      _set: {
        isSelected: suggestionItems?.isSelected,
        selectedByUserId: userId,
      },
    });
  });
  if (suggestionUpdate.length > 0) {
    const updateSuggestion = await updateSuggestionData({
      variables: {
        suggestionData: suggestionUpdate,
      },
    });
  }

  if (isIntrimAnswer) {
    //Update Interim Answer
    if (updatedAnswer.length > 0) {
      let updateInterimData: UpdateInterimAnswerByQuestionIdAndSubmissionIdMutationVariables =
        {
          Interim_AnswerUpdate: [],
        };
      let updateInterimanswer: any = [];
      updatedAnswer?.map(async (m: any) => {
        let values: any = !!m.data?.value ? m.data?.value : m.value;
        await updateInterimanswer.push({
          where: {
            submissionId: {
              _eq: submissionId,
            },
            formFieldId: {
              _eq: m.formFieldId,
            },
          },
          _set: {
            data: {
              value: values,
            },
          },
        });
      });
      updateInterimData.Interim_AnswerUpdate = updateInterimanswer;
      const result = await updateInterimAnswer({
        variables: {
          Interim_AnswerUpdate: updateInterimData.Interim_AnswerUpdate,
        },
      });
      if (pageName !== "AddComment") {
        updatedAnswer.map(async (item: any) => {
          if (item.value) {
            await UpdateChildRecommendationStatus(
              formFieldResponse?.data?.FormField,
              item.formFieldId,
              Answerdata,
              isIntrimAnswer,
              RecommendationsData?.data,
              false,
              storeData,
              item.formFieldId,
              updateRecommendation
            );
          }
        });
      }
    }
    if (InsertInterimAnsweronUpdateData.length > 0) {
      //Insert child questions answer in Interim Answer
      const answerInsertData: Interim_Answer_Insert_Input[] = [];
      InsertInterimAnsweronUpdateData.map(async (x: any) => {
        await answerInsertData.push({
          submissionId: submissionId,
          questionId: x.questionId,
          formFieldId: x.formFieldId,
          data: {
            value: x.value,
          },
          created_by: userId,
          updated_by: userId,
        });
      });
      const insertintoInterimAnser = await insertInterimAnsweronUpdate({
        variables: {
          interinm_input: answerInsertData,
        },
      });
    }
    if (pageName == "Recommendation") {
      let executeAPI: boolean = false;
      if (
        updatedAnswer?.length > 0 ||
        InsertInterimAnsweronUpdateData?.length > 0
      ) {
        executeAPI = true;
      } else if (recommendationStatus === RecommendationStatus.Closed) {
        executeAPI = true;
      }
      if (executeAPI) {
        let interimAnswerId = null;
        let interimAnswerData = data?.Interim_Answer.filter(
          (rec: any) => rec.formFieldId === formFieldId
        );

        if (interimAnswerData.length > 0) {
          if (!!interimAnswerData[0].Interim_Answer) {
            interimAnswerId = interimAnswerData[0].Interim_Answer?.id;
          } else {
            interimAnswerId = interimAnswerData[0]?.id;
          }
        }
        await fetch("/api/progress-report-score", {
          method: "POST",
          body: JSON.stringify({
            formId: formId,
            submissionId,
            invitationId,
            isApproved: "true",
            questionId,
            interimAnswerId: interimAnswerId || null,
          }),
          headers: {
            "Content-Type": "application/json",
            Authorization: userSession?.accessToken ?? "",
          },
        });
      }
    } else if (pageName == "AddComment" || pageName == "GroupWizard") {
      if (
        updatedAnswer?.length > 0 ||
        InsertInterimAnsweronUpdateData?.length > 0
      ) {
        let interimAnswerId = null;
        let interimAnswerData = data?.Interim_Answer.filter(
          (rec: any) => rec.formFieldId === formFieldId
        );

        if (interimAnswerData.length > 0) {
          if (!!interimAnswerData[0].Interim_Answer) {
            interimAnswerId = interimAnswerData[0].Interim_Answer?.id;
          } else {
            interimAnswerId = interimAnswerData[0]?.id;
          }
        }

        await fetch("/api/progress-report-score", {
          method: "POST",
          body: JSON.stringify({
            formId: formId,
            submissionId,
            invitationId,
            isApproved: "true",
            questionId,
            interimAnswerId: interimAnswerId || null,
          }),
          headers: {
            "Content-Type": "application/json",
            Authorization: userSession?.accessToken ?? "",
          },
        });
      }
    } else {
      if (
        updatedAnswer?.length > 0 ||
        InsertInterimAnsweronUpdateData?.length > 0
      ) {
        const interimAnswerId = data?.Interim_Answer.filter(
          (rec: any) => rec.Interim_Answer
        )[0]?.id;
        await fetch("/api/progress-report-score", {
          method: "POST",
          body: JSON.stringify({
            formId: formId,
            submissionId,
            invitationId,
            isApproved: "true",
            questionId,
            interimAnswerId: interimAnswerId || null,
          }),
          headers: {
            "Content-Type": "application/json",
            Authorization: userSession?.accessToken ?? "",
          },
        });
      }
    }
  } else {
    if (
      !!data &&
      !!data?.Answer?.filter((x: any) => x?.Interim_Answers.length > 0) &&
      data?.Answer?.filter((x: any) => x?.Interim_Answers.length > 0).length > 0
    ) {
      if (updatedAnswer.length > 0) {
        let updateAnswerData: UpdateAnswerMutationVariables = {
          AnswerUpdate: [],
        };
        let updateAnswerdatatopush: any = [];
        updatedAnswer?.map(async (m: any) => {
          let values: any = !!m.data?.value ? m.data?.value : m.value;
          updateAnswerdatatopush.push({
            where: {
              submissionId: {
                _eq: submissionId,
              },
              formFieldId: {
                _eq: m.formFieldId,
              },
            },
            _set: {
              data: {
                value: values,
              },
            },
          });
        });
        updateAnswerData.AnswerUpdate = updateAnswerdatatopush;
        const returnupdateAnswer = await updateAnswer({
          variables: {
            AnswerUpdate: updateAnswerData.AnswerUpdate,
          },
        });
        if (pageName !== "AddComment") {
          updatedAnswer.map(async (item: any) => {
            if (item.value) {
              await UpdateChildRecommendationStatus(
                formFieldResponse?.data?.FormField,
                item.formFieldId,
                Answerdata,
                isIntrimAnswer,
                RecommendationsData?.data,
                true,
                storeData,
                item.formFieldId,
                updateRecommendation
              );
            }
          });
        }
      }
      if (InsertAnswerOnUpdate.length > 0) {
        //Insert child questions answer in Answer table
        const answerBulkInsertData: Answer_Insert_Input[] = [];
        InsertAnswerOnUpdate.map((x: any) => {
          answerBulkInsertData.push({
            submissionId: submissionId,
            questionId: x.questionId,
            formFieldId: x.formFieldId,
            data: {
              value: x.value,
            },
            created_by: userId,
            updated_by: userId,
          });
        });
        const insertintoAnser = await insertBulkAnswer({
          variables: {
            answerData: answerBulkInsertData,
          },
        });
      }
    } else if (
      !!data &&
      !!data?.Answer?.filter((x: any) => x?.Interim_Answers.length == 0) &&
      data?.Answer?.filter((x: any) => x?.Interim_Answers.length == 0).length ==
        data?.Answer?.length
    ) {
      const AnswerUpsertData: Answer_Insert_Input[] = [];
      OtherAnswerData?.map((m: any) => {
        AnswerUpsertData.push({
          submissionId: submissionId,
          questionId: m.questionId,
          formFieldId: m.formFieldId,
          data: {
            value: m.value,
          },
          created_by: userId,
        });
      });
      if (
        AnswerUpsertData != undefined &&
        AnswerUpsertData != null &&
        AnswerUpsertData.length > 0
      ) {
        const result = await upsertAnswer({
          variables: {
            answerData: AnswerUpsertData,
            submissionId: submissionId,
            questionId: questionId,
          },
        });
      }
    }
  }
};
let changesDataArray: any = [];
const UpdateChildRecommendationStatus = async (
  maindata: any,
  formFieldId: any,
  answers: any,
  isIntrimAnswer: boolean,
  RecommendationsData: any,
  isDraftedCarry: boolean,
  storeData: any,
  stableFormFieldId: any,
  updateRecommendation: any
) => {
  const today = new Date();
  const answerDetails = storeData;
  // const maindata: any = [];
  // maindata.push(...da);
  if (maindata !== undefined && maindata?.length > 0) {
    let formFieldData: any = maindata?.filter(
      (field: any) => field.id === formFieldId
    );
    if (!!formFieldData && formFieldData.length > 0) {
      if (
        maindata.filter((d: any) => d.groupField === formFieldData[0].field)
          ?.length > 0
      ) {
        maindata
          .filter((d: any) => d.groupField === formFieldData[0].field)
          ?.map(async (child: any) => {
            if (!!child.displayRules && child.displayRules.length > 0) {
              const IsVisible = Boolean(
                jsonata(child.displayRules[0].rule).evaluate(answerDetails)
              )
                ? true
                : false;
              let mainRecommendationdata: any =
                RecommendationsData?.Interim_Answer.filter(
                  (ans: any) => ans.formFieldId === child.id
                );
              if (isDraftedCarry) {
                mainRecommendationdata = RecommendationsData?.Answer.filter(
                  (ans: any) => ans.formFieldId === child.id
                );
              }
              if (
                !!mainRecommendationdata &&
                mainRecommendationdata?.length > 0
              ) {
                if (isDraftedCarry) {
                  if (
                    !!mainRecommendationdata[0]?.Interim_Answers &&
                    mainRecommendationdata[0]?.Interim_Answers.length > 0
                  ) {
                    mainRecommendationdata[0]?.Interim_Answers[0]?.Interim_Recommendations.filter(
                      (s: any) => s.status != RecommendationStatus.Closed
                    )?.map((recom: any) => {
                      changesDataArray.push({
                        where: {
                          id: {
                            _eq: recom.id,
                          },
                        },
                        _set: {
                          status: IsVisible
                            ? recom.status == RecommendationStatus.NA
                              ? RecommendationStatus.Open
                              : recom.status
                            : RecommendationStatus.NA,
                          updated_at: today,
                        },
                      });
                    });
                  }
                  if (
                    !!mainRecommendationdata[0]?.Interim_Answers[0]
                      ?.Interim_Answer
                  ) {
                    mainRecommendationdata[0]?.Interim_Answers[0]?.Interim_Answer?.Interim_Recommendations.filter(
                      (s: any) => s.status != RecommendationStatus.Closed
                    )?.map((recom: any) => {
                      changesDataArray.push({
                        where: {
                          id: {
                            _eq: recom.id,
                          },
                        },
                        _set: {
                          status: IsVisible
                            ? recom.status == RecommendationStatus.NA
                              ? RecommendationStatus.Open
                              : recom.status
                            : RecommendationStatus.NA,
                          updated_at: today,
                        },
                      });
                    });
                  }
                  if (
                    !!mainRecommendationdata[0]?.Interim_Answers[0]
                      ?.Interim_Answers &&
                    mainRecommendationdata[0]?.Interim_Answers[0]
                      ?.Interim_Answers.length > 0
                  ) {
                    mainRecommendationdata[0]?.Interim_Answers[0]?.Interim_Answers?.Interim_Recommendations?.filter(
                      (s: any) => s.status != RecommendationStatus.Closed
                    )?.map((recom: any) => {
                      changesDataArray.push({
                        where: {
                          id: {
                            _eq: recom.id,
                          },
                        },
                        _set: {
                          status: IsVisible
                            ? recom.status == RecommendationStatus.NA
                              ? RecommendationStatus.Open
                              : recom.status
                            : RecommendationStatus.NA,
                          updated_at: today,
                        },
                      });
                    });
                  }
                } else {
                  mainRecommendationdata[0]?.Interim_Recommendations.filter(
                    (s: any) => s.status != RecommendationStatus.Closed
                  )?.map((recom: any) => {
                    changesDataArray.push({
                      where: {
                        id: {
                          _eq: recom.id,
                        },
                      },
                      _set: {
                        status: IsVisible
                          ? recom.status == RecommendationStatus.NA
                            ? RecommendationStatus.Open
                            : recom.status
                          : RecommendationStatus.NA,
                        updated_at: today,
                      },
                    });
                  });
                  if (!!mainRecommendationdata[0]?.Interim_Answer) {
                    mainRecommendationdata[0]?.Interim_Answer?.Interim_Recommendations.filter(
                      (s: any) => s.status != RecommendationStatus.Closed
                    )?.map((recom: any) => {
                      changesDataArray.push({
                        where: {
                          id: {
                            _eq: recom.id,
                          },
                        },
                        _set: {
                          status: IsVisible
                            ? recom.status == RecommendationStatus.NA
                              ? RecommendationStatus.Open
                              : recom.status
                            : RecommendationStatus.NA,
                          updated_at: today,
                        },
                      });
                    });
                  }
                }
              }
            }
            await UpdateChildRecommendationStatus(
              maindata,
              child.id,
              answers,
              isIntrimAnswer,
              RecommendationsData,
              isDraftedCarry,
              storeData,
              stableFormFieldId,
              updateRecommendation
            );
          });
      } else {
        let data = maindata.filter(
          (x: any) =>
            x.id === formFieldData[0]?.id && x.id !== stableFormFieldId
        );
        if (!!data && data.length > 0) {
          if (!!data[0]?.displayRules && data[0]?.displayRules.length > 0) {
            const IsVisible = Boolean(
              jsonata(data[0]?.displayRules[0].rule).evaluate(answerDetails)
            )
              ? true
              : false;
            const ansData = answers.filter(
              (ans: any) => ans.formFieldId === formFieldData[0]?.id
            );
            let mainRecommendationdata: any =
              RecommendationsData?.Interim_Answer.filter(
                (ans: any) => ans.formFieldId === formFieldData[0]?.id
              );
            if (isDraftedCarry) {
              mainRecommendationdata = RecommendationsData?.Answer.filter(
                (ans: any) => ans.formFieldId === formFieldData[0]?.id
              );
            }
            if (
              !!mainRecommendationdata &&
              mainRecommendationdata?.length > 0
            ) {
              if (isDraftedCarry) {
                if (
                  !!mainRecommendationdata[0]?.Interim_Answers &&
                  mainRecommendationdata[0]?.Interim_Answers.length > 0
                ) {
                  mainRecommendationdata[0]?.Interim_Answers[0]?.Interim_Recommendations.filter(
                    (s: any) => s.status != RecommendationStatus.Closed
                  )?.map((recom: any) => {
                    changesDataArray.push({
                      where: {
                        id: {
                          _eq: recom.id,
                        },
                      },
                      _set: {
                        status: IsVisible
                          ? recom.status == RecommendationStatus.NA
                            ? RecommendationStatus.Open
                            : recom.status
                          : RecommendationStatus.NA,
                        updated_at: today,
                      },
                    });
                  });
                }
                if (
                  !!mainRecommendationdata[0]?.Interim_Answers[0]
                    ?.Interim_Answer
                ) {
                  mainRecommendationdata[0]?.Interim_Answers[0]?.Interim_Answer?.Interim_Recommendations.filter(
                    (s: any) => s.status != RecommendationStatus.Closed
                  )?.map((recom: any) => {
                    changesDataArray.push({
                      where: {
                        id: {
                          _eq: recom.id,
                        },
                      },
                      _set: {
                        status: IsVisible
                          ? recom.status == RecommendationStatus.NA
                            ? RecommendationStatus.Open
                            : recom.status
                          : RecommendationStatus.NA,
                        updated_at: today,
                      },
                    });
                  });
                }
                if (
                  !!mainRecommendationdata[0]?.Interim_Answers[0]
                    ?.Interim_Answers &&
                  mainRecommendationdata[0]?.Interim_Answers[0]?.Interim_Answers
                    .length > 0
                ) {
                  mainRecommendationdata[0]?.Interim_Answers[0]?.Interim_Answers?.Interim_Recommendations?.filter(
                    (s: any) => s.status != RecommendationStatus.Closed
                  )?.map((recom: any) => {
                    changesDataArray.push({
                      where: {
                        id: {
                          _eq: recom.id,
                        },
                      },
                      _set: {
                        status: IsVisible
                          ? recom.status == RecommendationStatus.NA
                            ? RecommendationStatus.Open
                            : recom.status
                          : RecommendationStatus.NA,
                        updated_at: today,
                      },
                    });
                  });
                }
              } else {
                mainRecommendationdata[0]?.Interim_Recommendations.filter(
                  (s: any) => s.status != RecommendationStatus.Closed
                )?.map((recom: any) => {
                  changesDataArray.push({
                    where: {
                      id: {
                        _eq: recom.id,
                      },
                    },
                    _set: {
                      status: IsVisible
                        ? recom.status == RecommendationStatus.NA
                          ? RecommendationStatus.Open
                          : recom.status
                        : RecommendationStatus.NA,
                      updated_at: today,
                    },
                  });
                });
                if (
                  !!mainRecommendationdata[0]?.Interim_Answer &&
                  mainRecommendationdata[0]?.Interim_Answer.length > 0
                ) {
                  mainRecommendationdata[0]?.Interim_Answer[0]?.Interim_Recommendations.filter(
                    (s: any) => s.status != RecommendationStatus.Closed
                  )?.map((recom: any) => {
                    changesDataArray.push({
                      where: {
                        id: {
                          _eq: recom.id,
                        },
                      },
                      _set: {
                        status: IsVisible
                          ? recom.status == RecommendationStatus.NA
                            ? RecommendationStatus.Open
                            : recom.status
                          : RecommendationStatus.NA,
                        updated_at: today,
                      },
                    });
                  });
                }
              }
            }
          }
        }
      }
    }
  }
  if (changesDataArray && changesDataArray?.length > 0) {
    let updatebulkrecommendation: BulkUpdateInterimRecommendationByInterimAnswerIdMutationVariables =
      {
        Interim_Recommendation: [],
      };
    updatebulkrecommendation.Interim_Recommendation = changesDataArray;
    const results = await updateRecommendation({
      variables: {
        Interim_Recommendation: updatebulkrecommendation.Interim_Recommendation,
      },
    });
  }
};
//// save Answer End

export const isAIFeaturedEnabledorNot = (
  formDetails: Form[],
  assessorConsultantMappingData: AssessorConsultantMapping[],
  globalMasterData: GlobalMaster[],
  isfromLogin: Boolean,
  userList?: Record<string, any>[],
  userRole?: String,
  userId?: String,
  companyId?: String,
  InvitationDetail?: getAIFeatureByDBType[]
) => {
  let VCuserData: Record<string, any>[] = [];
  if (isfromLogin) {
    if (
      String(userRole).toLocaleLowerCase() ==
      String(AppRoles.Consultant).toLocaleLowerCase()
    ) {
      assessorConsultantMappingData
        .filter((items) => items.consultantCompanyId == companyId)
        .forEach((items) =>
          items?.Company?.Users.forEach((dataItem) =>
            dataItem.UserRoles.filter(
              (roleitem) => roleitem.roleName == AppRoles.Inviter
            ).forEach((roleItem) => {
              if (
                VCuserData.filter((data) => data.vcuserId == roleItem.userId)
                  .length == 0
              ) {
                VCuserData.push({
                  vcuserId: roleItem.userId,
                  consultants: [],
                });
              }
            })
          )
        );
    } else if (
      String(userRole).toLocaleLowerCase() ==
      String(AppRoles.Inviter).toLocaleLowerCase()
    ) {
      VCuserData.push({ vcuserId: String(userId), consultants: [] });
    } else {
      if (!!userList) {
        const userConsultantCompany = userList
          .filter((items) => items.role == AppRoles.Consultant)
          .map((items) => items.companyId);
        if (userConsultantCompany.length > 0) {
          const vcCompany = assessorConsultantMappingData.filter((items) =>
            userConsultantCompany.some(
              (cItems) => cItems == items.assessorCompanyId
            )
          );
          const consultantCompanyList = assessorConsultantMappingData.filter(
            (items) =>
              userConsultantCompany.some(
                (cItems) => cItems == items.consultantCompanyId
              )
          );
          vcCompany.forEach((items) =>
            items?.Company?.Users.forEach((dataItem) =>
              dataItem.UserRoles.filter(
                (roleitem) => roleitem.roleName == AppRoles.Inviter
              ).forEach((roleItem) => {
                if (
                  VCuserData.filter((data) => data.vcuserId == roleItem.userId)
                    .length == 0
                ) {
                  VCuserData.push({
                    vcuserId: roleItem.userId,
                    consultants: userList
                      .filter(
                        (userItems) =>
                          userItems.role == AppRoles.Consultant &&
                          userItems.companyId == items.assessorCompanyId
                      )
                      .map((items) => items.userId),
                  });
                }
              })
            )
          );
          consultantCompanyList.forEach((items) =>
            items?.Company?.Users.forEach((dataItem) =>
              dataItem.UserRoles.filter(
                (roleitem) => roleitem.roleName == AppRoles.Inviter
              ).forEach((roleItem) => {
                if (
                  VCuserData.filter((data) => data.vcuserId == roleItem.userId)
                    .length == 0
                ) {
                  VCuserData.push({
                    vcuserId: roleItem.userId,
                    consultants: userList
                      .filter(
                        (userItems) =>
                          userItems.role == AppRoles.Consultant &&
                          userItems.companyId == items.consultantCompanyId
                      )
                      .map((items) => items.userId),
                  });
                }
              })
            )
          );
        }
        VCuserData = [
          ...VCuserData,
          ...userList
            .filter((items) => items.role == AppRoles.Inviter)
            .map((items) => {
              return { vcuserId: items.userId, consultants: [] };
            }),
        ];
      }
    }
  } else {
    if (!!InvitationDetail)
      InvitationDetail.filter((datas) => !!datas.created_by).forEach(
        (Listitems) => {
          switch (String(Listitems.userRole).toLocaleUpperCase()) {
            case String(AppRoles.Inviter).toLocaleUpperCase():
              VCuserData.push({
                vcuserId: String(Listitems.created_by),
                consultants: [],
              });
              break;
            case String(AppRoles.Consultant).toLocaleUpperCase():
              assessorConsultantMappingData
                ?.filter(
                  (items) => items.consultantCompanyId == Listitems?.companyId
                )
                .forEach((items) =>
                  items?.Company?.Users.forEach((dataItem) =>
                    dataItem.UserRoles.filter(
                      (roleitem) => roleitem.roleName == AppRoles.Inviter
                    ).forEach((roleItem) => {
                      if (
                        VCuserData.filter(
                          (data) => data.vcuserId == roleItem.userId
                        ).length == 0
                      ) {
                        VCuserData.push({
                          vcuserId: roleItem.userId,
                          consultants: [],
                        });
                      }
                    })
                  )
                );
              break;
            default:
              break;
          }
        }
      );
  }
  const VCuserAIPlan: Record<string, any>[] = [];
  globalMasterData?.filter((items: any) =>
    items.data.filter((item: string) => {
      const vcUserDetail = VCuserData.filter((vitem) => vitem.vcuserId == item);
      if (vcUserDetail.length > 0) {
        VCuserAIPlan.push({
          VCuserID: item,
          consultants: vcUserDetail[0].consultants,
          docWithAI: Platform[0].AITypes.filter(
            (planItems) => planItems.name == items.type
          )[0].docWithAI,
          onlyDoc: Platform[0].AITypes.filter(
            (planItems) => planItems.name == items.type
          )[0].onlyDoc,
        });
      }
    })
  );
  const FormDataPointDetails: userInvitationAIStatus[] = [];
  formDetails?.map((items) => {
    if (VCuserAIPlan.length > 0) {
      VCuserAIPlan.forEach((vcItems) => {
        FormDataPointDetails.push({
          formId: items?.id,
          vcUserId: vcItems?.VCuserID,
          consultants: vcItems?.consultants,
          isAIDataPointsAdded: items?.isAIDataPointsAdded,
          docWithAI: vcItems?.docWithAI,
          onlyDoc: vcItems?.onlyDoc,
        });
      });
    } else {
      FormDataPointDetails.push({
        formId: items?.id,
        vcUserId: undefined,
        isAIDataPointsAdded: items?.isAIDataPointsAdded,
        docWithAI: false,
        onlyDoc: false,
        consultants: [],
      });
    }
  });
  return FormDataPointDetails;
};

export const AIFeatureForInvitationId = (
  formId: string,
  isInvitedByConsultant: boolean,
  created_by: string
) => {
  if (typeof window !== "undefined") {
    const decodedToken: any = jwt.decode(String(userSession?.accessToken));
    const AITokenValue = !!decodedToken
      ? decodedToken["https://hasura.io/jwt/claims"]["x-hasura-form-with-AI"]
      : null;
    const AIData: userInvitationAIStatus[] =
      !!AITokenValue &&
      AITokenValue?.filter((items: any) => items?.formId == formId).length > 0
        ? AITokenValue?.filter(
            (items: any) =>
              items?.formId == formId &&
              (userSession?.user?.role === AppRoles.Invitee &&
              isInvitedByConsultant
                ? items.consultants.filter(
                    (cItem: string) => cItem == created_by
                  ).length > 0
                : items?.vcUserId == created_by)
          )
        : [];
    return AIData;
  } else {
    return [];
  }
};

export const AIFeatureForInvitationIdByDB = async (
  InvitationDetail: getAIFeatureByDBType[]
) => {
  const AIData: InvitationIdAIDetails[] = [];
  const formDetails = await sdk.formWithDataPoints();
  const assessorConsultantMappingData =
    await sdk.getAssessorConsultantMapping();
  const globalMasterAIData = await sdk.getGlobalMasterByTypeList({
    type: Platform[0].AITypes.map((items) => items.name),
  });
  if (
    !!globalMasterAIData?.GlobalMaster &&
    globalMasterAIData?.GlobalMaster.length > 0
  ) {
    const formData = isAIFeaturedEnabledorNot(
      formDetails?.Form as Form[],
      assessorConsultantMappingData?.AssessorConsultantMapping as AssessorConsultantMapping[],
      globalMasterAIData?.GlobalMaster?.map((items: Record<string, any>) => {
        return { type: items.type, data: items.data };
      }) as GlobalMaster[],
      false,
      [],
      "",
      "",
      "",
      InvitationDetail
    );
    InvitationDetail.forEach((items) => {
      if (
        !!formData &&
        formData?.filter((dataItems: any) => dataItems?.formId == items.formId)
          .length > 0
      ) {
        const formWithAIData = formData?.filter(
          (formItems: any) =>
            formItems?.formId == items.formId &&
            (userSession?.user?.role === AppRoles.Invitee &&
            items.isInvitedByConsultant
              ? formItems.consultants.filter(
                  (cItem: string) => cItem == items.created_by
                ).length > 0
              : formItems?.vcUserId == items.created_by)
        );
        formWithAIData.forEach((itemData) => {
          AIData.push({
            invitationId: items?.id,
            AIDetail: itemData,
          });
        });
      }
    });
  }
  return AIData;
};

export const getAISuggestionCarouselData = (
  suggestionData: GetRenderFormFieldDetailsQuery["FormInvitation"][0]["Suggestions"],
  fieldInterface?: string
) => {
  const isMultiDropdown: boolean =
    String(fieldInterface) == "select-multiple-dropdown";
  const AISuggestionCarouseldata: AICArouselData[] = [];
  suggestionData.forEach((suggestionItems) => {
    const isAnySelected =
      suggestionData.filter((selectedItem) => selectedItem.isSelected == true)
        .length > 0
        ? true
        : false;
    let allSources = suggestionItems?.SuggestionSourceMappings.map(
      (sourceItems) => {
        return {
          sourceItem: sourceItems?.Source,
          pageNo: sourceItems?.suggestionPageNo,
          sourceType: sourceItems?.Source?.type,
          fileUrl: sourceItems?.Source?.SourceFile?.originalFileUrl,
          infoIconText: sourceItems?.suggestionInfoContent,
        };
      }
    );
    allSources = allSources.sort((a, b) =>
      b.sourceItem?.type > a.sourceItem?.type ? -1 : 1
    );
    const suggestionCategories = allSources.map((dataItems, index) => {
      if (index > 0) {
        return {
          categoryName:
            dataItems?.sourceItem?.type == SourcesType.Uploaded?.dbTittle
              ? SourcesType.Uploaded?.frontEndTitle
              : SourcesType.Web?.frontEndTitle,
          description:
            dataItems?.sourceType == SourcesType.Uploaded?.dbTittle
              ? ""
              : dataItems?.fileUrl,
          docs: [
            {
              docName: dataItems?.sourceItem?.SourceFile?.originalFileName,
              docUrl:
                !!dataItems?.pageNo && dataItems?.pageNo > 0
                  ? String(dataItems?.sourceItem?.SourceFile?.originalFileUrl) +
                    "#page=" +
                    dataItems?.pageNo
                  : String(dataItems?.sourceItem?.SourceFile?.originalFileUrl),
              pageNo: !!dataItems?.pageNo ? Number(dataItems?.pageNo) : 0,
              tooltipLabelText: dataItems?.infoIconText,
            },
          ],
          isOptions: false,
        };
      }
    }) as suggestionCategory[];
    let suggestionForSameInput = [];
    const SourceTypeforSameInput: Record<string, any>[] = [];
    if (!isAnySelected) {
      suggestionForSameInput = suggestionData?.filter(
        (items) => items.formFieldId == suggestionItems?.formFieldId
      );
      suggestionForSameInput.forEach((data) => {
        data?.SuggestionSourceMappings.forEach((items) => {
          if (items.Source?.type == SourcesType?.Uploaded?.dbTittle) {
            SourceTypeforSameInput.push({
              suggestionId: data?.id,
              type: items.Source?.type,
            });
          }
        });
      });
    }
    const suggestionIdArray = SourceTypeforSameInput.map(
      (data) => data.suggestionId
    );
    const distinctSuggestionId = suggestionIdArray.filter(
      (item, index) => suggestionIdArray.indexOf(item) === index
    );
    const suggestionList: suggestionCategory[] = [];
    if (Array.isArray(suggestionItems?.suggestion?.value)) {
      suggestionItems?.suggestion?.value.forEach((item: any, index: number) => {
        if (index > 0) {
          suggestionList.push({
            categoryName: SourcesType?.Options?.frontEndTitle,
            description: isMultiDropdown
              ? !!item?.value
                ? item?.value
                : item
              : item,
            docs: [],
            isOptions: true,
          });
        }
      });
    }
    const isConflict = isAnySelected
      ? false
      : distinctSuggestionId.length > 1 &&
        distinctSuggestionId.filter((items) => items == suggestionItems?.id)
          .length > 0
      ? true
      : false;
    AISuggestionCarouseldata.push({
      id: suggestionItems?.id,
      cardType: allSources.length > 0 ? allSources[0]?.sourceItem?.type : "",
      defaultData: {
        pageNo: allSources.length > 0 ? Number(allSources[0]?.pageNo) : 0,
        infoContent:
          allSources.length > 0
            ? !!allSources[0]?.infoIconText
              ? String(allSources[0]?.infoIconText)
              : ""
            : "",
      },
      title: Array.isArray(suggestionItems?.suggestion?.value)
        ? isMultiDropdown
          ? !!suggestionItems?.suggestion?.value[0].value
            ? suggestionItems?.suggestion?.value[0].value
            : suggestionItems?.suggestion?.value[0]
          : suggestionItems?.suggestion?.value[0]
        : suggestionItems?.suggestion?.value,
      suggestionCount: Array.isArray(suggestionItems?.suggestion?.value)
        ? suggestionItems?.suggestion?.value?.length - 1
        : 0,
      suggestion: {
        suggestionCategory: suggestionList,
      },
      allSuggestions: suggestionItems?.suggestion?.value,
      sourceUrl:
        allSources.length > 0
          ? !!allSources[0]?.pageNo && allSources[0]?.pageNo > 0
            ? String(allSources[0]?.sourceItem?.SourceFile?.originalFileUrl) +
              "#page=" +
              allSources[0]?.pageNo
            : String(allSources[0]?.sourceItem?.SourceFile?.originalFileUrl)
          : "",
      sourceTitle:
        allSources.length > 0
          ? allSources[0]?.sourceItem?.type == SourcesType?.Uploaded?.dbTittle
            ? String(allSources[0]?.sourceItem?.SourceFile?.originalFileName)
            : String(allSources[0]?.sourceItem?.SourceFile?.originalFileUrl)
          : "",
      sourceCount: allSources.length - 1,
      suggestionlist: {
        suggestionCategory: suggestionCategories,
      },
      conflict: isConflict,
      isSeletect: suggestionItems?.isSelected,
      formFieldId: suggestionItems?.formFieldId,
      HTMLSuggestion: suggestionItems?.suggestion,
    });
  });
  return AISuggestionCarouseldata;
};

export const getAISuggestionCarouselforFileData = (
  sourceData: GetSourceDataByInvitationIdQuery["Sources"],
  formFieldId: string
) => {
  const AISuggestionCarouseldata: AICArouselData[] = [];
  sourceData.forEach((sourceItems) => {
    if (sourceItems.type == SourcesType.Uploaded?.dbTittle) {
      AISuggestionCarouseldata.push({
        id: sourceItems?.id,
        cardType: "",
        defaultData: {
          pageNo: 0,
          infoContent: "",
        },
        title: String(sourceItems?.SourceFile?.originalFileName),
        suggestionCount: 0,
        suggestion: {
          suggestionCategory: [],
        },
        allSuggestions: [
          {
            fileurl: String(sourceItems?.SourceFile?.originalFileUrl),
            fileName: String(sourceItems?.SourceFile?.originalFileName),
          },
        ],
        sourceUrl: String(sourceItems?.SourceFile?.originalFileUrl),
        sourceTitle: String(sourceItems?.SourceFile?.originalFileName),
        sourceCount: 0,
        suggestionlist: {
          suggestionCategory: [],
        },
        conflict: false,
        isSeletect: false,
        formFieldId: formFieldId,
      });
    }
  });
  return AISuggestionCarouseldata.sort((a, b) =>
    b.conflict < a.conflict ? -1 : 1
  ).sort((a, b) => {
    if (a.isSeletect && !b.isSeletect) return -1;
    if (!a.isSeletect && b.isSeletect) return 1;
    return 0;
  });
};

export const urlToFile = async (url: string, filename: string) => {
  try {
    // Fetch the file from the URL
    const response = await fetch(url);

    // Ensure the fetch was successful
    if (!response.ok) {
      throw new Error(`Failed to fetch file: ${response.statusText}`);
    }

    // Convert the response to a Blob
    const blob = await response.blob();

    // Create a File from the Blob
    return new File([blob], filename, { type: blob.type });
  } catch (error) {
    console.error("Error converting URL to File:", error);
    throw error;
  }
};

export const getWebCurationData = async (
  formId: string,
  invitationId: string
) => {
  let AIcardData: AIDataCardsType = {
    totalInputsRequired: 0,
    dataCapturedUsingAI: 0,
    dataInputsWithMultipleValues: 0,
    totalTimeSaved: 0,
    pendingDataPoints: 0,
    mandatoryFieldsCount: 0,
    optionalFieldsCount: 0,
    timeSavedMinutes: 0,
    timeSavedUnit: "hrs",
  };
  if (!!formId && !!invitationId) {
    await fetch(
      process.env.NEXT_PUBLIC_API_BASE_URL + "/api/AI/AI-dataStats-calculation",
      {
        method: "POST",
        headers: {
          "content-type": "application/json",
        },
        body: JSON.stringify({
          formId: formId,
          invitationId: invitationId,
        }),
      }
    )
      .then((response) => response.json())
      .then(async (apiData) => {
        AIcardData.totalInputsRequired = apiData.data.totalInputsRequired;
        AIcardData.dataCapturedUsingAI = apiData.data.dataCapturedUsingAI;
        AIcardData.dataInputsWithMultipleValues =
          apiData.data.dataInputsWithMultipleValues;
        AIcardData.totalTimeSaved = apiData.data.totalTimeSaved;
        AIcardData.pendingDataPoints = apiData.data.pendingDataPoints;
        AIcardData.mandatoryFieldsCount = apiData.data.mandatoryFieldsCount;
        AIcardData.optionalFieldsCount = apiData.data.optionalFieldsCount;
        AIcardData.timeSavedMinutes = apiData.data.timeSavedMinutes;
        AIcardData.timeSavedUnit = apiData.data.timeSavedUnit;
      })
      .catch((error) => {
        console.error("Error:", error);
      });
  }
  return AIcardData;
};

export const formatFileName = (fileName: string): string => {
  // Remove file extension (e.g., '.pdf', '.txt')
  let formattedName = fileName.replace(/\.[^/.]+$/, "");

  // Replace non-alphanumeric characters with a space, and treat consecutive special chars as one space
  formattedName = formattedName.replace(/[^a-zA-Z0-9]+/g, " ").trim();

  // Capitalize the first letter of each word
  formattedName = formattedName.replace(/\b\w/g, (char) => char.toUpperCase());

  return formattedName;
};
export const calculateEstimatedTime = (
  inputCount: number,
  perInputTime: number
) => {
  let totalTime: number = inputCount * perInputTime;
  //let totalTimeUnit = " hrs";
  // if (totalTime > 60) {
  //   const hours = Math.floor(totalTime / 60);
  //   const minutes = totalTime % 60;
  //   totalTime = minutes >= 30 ? hours + 0.3 : hours;
  // } else {
  //   totalTimeUnit = " min";
  //   if (totalTime > 0 && totalTime <= 15) {
  //     totalTime = 15;
  //   } else if (totalTime > 15 && totalTime <= 30) {
  //     totalTime = 30;
  //   } else if (totalTime > 30 && totalTime <= 60) {
  //     totalTime = 30;
  //   }
  // }
  // return totalTime + totalTimeUnit;
  const estimatedTime = Math.floor(totalTime / 60);
  return estimatedTime < 1 ? "1 hr" : estimatedTime + " hrs";
};

export const postParentMessage = (message: string) =>
  window.parent?.postMessage(message, "*");

export const isURLValid = (location: string | undefined): boolean => {
  if (!location) return false;

  // Regular expression to match an S3 URL
  const s3UrlPattern = /^https?:\/\/s3\.[a-z0-9-]+\.amazonaws\.com\//i;

  return s3UrlPattern.test(location);
};

export function extractAllChildren(formFieldObj: FormFieldWithChildrenType) {
  let allChildrenList: any[] = [];
  let childs = new Set<FormFieldWithChildrenType>(
    formFieldObj?.children?.length
      ? [...formFieldObj.children, formFieldObj]
      : [{ ...formFieldObj }]
  );

  while (childs.size > 0) {
    const currentChild = Array.from(childs).pop(); // Get the last child
    if (!currentChild) continue;

    childs.delete(currentChild); // Remove it from the set

    if (currentChild.type !== "container" && currentChild.type !== "label") {
      allChildrenList.push(currentChild);
    }

    if (currentChild.children && currentChild.children.length > 0) {
      currentChild.children.forEach((child) => childs.add(child));
    }
  }
  return allChildrenList;
}

export function extractMultipleSelectChildren(
  formFieldObj: FormFieldWithChildrenType,
  answers: any
) {
  let allChildrenList: any[] = [];
  let childs = new Set<FormFieldWithChildrenType>(
    formFieldObj?.children?.length
      ? [...formFieldObj.children, formFieldObj]
      : [{ ...formFieldObj }]
  );

  while (childs.size > 0) {
    const currentChild = Array.from(childs).pop(); // Get the last child
    if (!currentChild) continue;

    childs.delete(currentChild); // Remove it from the set

    if (
      currentChild.interface === "select-multiple-dropdown" &&
      answers[currentChild?.field]?.value.length > 0
    ) {
      allChildrenList.push(currentChild);

      if (currentChild.children && currentChild.children.length > 0) {
        currentChild.children.forEach((child) => allChildrenList.push(child));
      }
    }

    if (currentChild.children && currentChild.children.length > 0) {
      currentChild.children.forEach((child) => childs.add(child));
    }
  }
  return allChildrenList;
}

export function extractGroupFieldIDwithAnswer(obj: any, answers: any) {
  return obj
    .filter((child: any) => child.field)
    .map((child: any) => ({
      field: child.field,
      value: answers[child?.field]?.value ? true : false,
      ans: answers[child?.field]?.value,
      required: child.fieldOptions.required,
    }));
}

export function flattenObjectValues(input: any) {
  let output: any = {};

  function processEntry(key: any, entry: any) {
    if (entry && typeof entry === "object" && !Array.isArray(entry)) {
      if ("value" in entry) {
        if (Array.isArray(entry.value)) {
          // Handling array values (nested objects)
          entry.value.forEach((item: any) => {
            if (item._id && "value" in item) {
              output[key] = { value: item.value };
            } else {
              output[key] = { value: true };
            }
            for (let subKey in item) {
              if (subKey !== "_id" && subKey !== "value") {
                output[subKey] = { value: item[subKey].value };
              }
            }
          });
        } else {
          output[key] = { value: entry.value };
        }
      }
    }
  }

  for (let key in input) {
    processEntry(key, input[key]);
  }

  return output;
}

export function extractValidChildren(formField: FormFieldWithChildrenType) {
  const validChildren = new Set();

  function processChildren(
    children: FormFieldWithChildrenType[],
    isParentContainer: boolean
  ) {
    for (const child of children) {
      if (child.type === "container" || child.type === "label") {
        // If the child is a container or label, process its children
        processChildren(child.children || [], false);
      } else {
        // Add child to set only if its parent is a container/label
        if (child?.fieldOptions?.enable) {
          validChildren.add(child);
        }
      }
    }
  }
  if (formField?.type !== "container" && formField?.type !== "label") {
    validChildren.add(formField);
  }
  processChildren(
    formField?.children || [],
    formField.type === "container" || formField.type === "label"
  );
  return Array.from(validChildren).length > 0
    ? Array.from(validChildren)
    : [formField];
}

export function refineVisibleFields(questions: any, answers: any) {
  let visibleFields = new Set();
  let queue = [...questions];

  while (queue.length > 0) {
    const field = queue.pop();

    if (field.displayRules && Array.isArray(field.displayRules)) {
      for (const ruleObj of field.displayRules) {
        if (ruleObj.rule) {
          const expression = jsonata(ruleObj.rule);
          const result = expression.evaluate(answers);

          if (result) {
            visibleFields.add(field);

            // Extract field names from the rule
            const fieldMatches = ruleObj.rule.match(/\b([a-zA-Z0-9_]+)\b/g);
            if (fieldMatches) {
              fieldMatches.forEach((fieldKey: any) => {
                const referencedField = questions.find(
                  (q: any) => q.field === fieldKey
                );
                if (referencedField) {
                  visibleFields.add(referencedField);
                }
              });
            }

            queue.push(
              ...questions.filter((q: any) =>
                q.displayRules?.some((dr: any) => dr.rule.includes(field.field))
              )
            );
          }
        }
      }
    }
  }

  return Array.from(visibleFields);
}
//to call Curation for AI
export const callAIAPI = async (
  formInvitationData: webCurationAPIDataType[]
) => {
  await fetch("/api/AI/AIprocessing", {
    method: "POST",
    headers: {
      "content-type": "application/json",
    },
    body: JSON.stringify({
      process: "newFormInvitation",
      data: formInvitationData,
    }),
  })
    .then((response) => {
      if (response?.statusText == "OK" && response?.status == 200) {
        return response.json();
      }
    })
    .then(async (resultdata) => {
      console.log(JSON.stringify(resultdata), "resultdata");
    });
};
export const LongTextTrim = (text: string, maxLength: number): string => {
  return text.length > maxLength ? text.slice(0, maxLength) + "..." : text;
};
