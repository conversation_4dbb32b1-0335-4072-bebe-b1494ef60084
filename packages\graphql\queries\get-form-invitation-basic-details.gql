query getFormInvitationbasicDetails(
  $invitationId: [uuid!]!
  $inputFields: [String!]!
) {
  FormInvitation(where: { id: { _in: $invitationId } }) {
    id
    FormSubmissions(where: { isActive: { _eq: true } }) {
      Answers {
        data
        formFieldId
        FormField {
          id
          type
          field
          questionId
          fieldOptions
        }
      }
    }
    Form {
      FormFields(
        where: { questionId: { _is_null: false }, type: { _in: $inputFields } }
      ) {
        id
        fieldOptions
        displayRules
        type
        questionId
        Question {
          key
        }
      }
    }
  }
}
