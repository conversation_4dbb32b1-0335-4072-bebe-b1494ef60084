import {
  ActionIcon,
  Divider,
  Group,
  Stack,
  Textarea,
  Tooltip,
} from "@mantine/core";
import { useUserSession } from "@warp/client/hooks/use-user-session";
import {
  AICArouselData,
  AppR<PERSON>s,
  blankCheck,
  FormMode,
  RecommendationStatus,
} from "@warp/shared/constants/app.constants";
import AISuggestionCarousel from "@warp/web/pages/embed/AIBasedSections/Common/AISuggestionCarousel";
import jsonata from "jsonata";
import { sortBy } from "lodash";
import { useRouter } from "next/router";
import { memo, useState } from "react";
import { FormFieldRender } from "..";
import SparkleSvgIcon from "../../../icons/SparkleSvgIcon";
import { getAISuggestionCarouselData } from "../common-functions";
import {
  useChildFieldState,
  useFormFieldControl,
  useFormFieldRemoveAnswerOnEnableFalse,
  useFormFieldStore,
  useInterimAnswerStore,
} from "../store";
import { FormFieldControl } from "../types";
import { validationErrorMessage } from "../validation.service";
import AddRecommendationButton from "./AddRecommendation";
import CommonRecommendation from "./CommonRecommendation";
import DisplayLabel from "./DisplayLabel";
let setErrorMessagedetails = false;

const InputMultiline: FormFieldControl<"input-multiline"> = ({ formField }) => {
  const userSession = useUserSession();
  let [getTextValue, setTextValue] = useState("");
  const [isclicked, setIsClicked] = useState(false);
  const state = useFormFieldControl<"input-multiline">(formField);

  const recommendationNewResponce: any = userSession?.GlobalMaster?.filter(
    (x: any) => x.type === "Recommendation_new"
  );

  // const { data: recommendationNewResponce } = useGetGlobalMasterByTypeQuery({
  //   variables: { type: "Recommendation_new" },
  // });

  let FormHasRecommendation: any = recommendationNewResponce[0]?.data.filter(
    (rec: any) => rec.FormId === formField?.formId
  );
  const checkInputData = (str: any) => {
    if (str === "") state.setValue(formField.id, "");
  };
  const { query } = useRouter();
  setErrorMessagedetails = state.setErrorMessage;
  const children = sortBy(formField.children ?? [], "seqIndex");

  useFormFieldRemoveAnswerOnEnableFalse(formField, state.fieldOptions.enable);
  if (!state.fieldOptions.enable) return <></>;
  //console.log("render", "InputMultiline", formField.field);
  if (getTextValue === "") getTextValue = state.value;
  if (
    (getTextValue === "" || getTextValue === undefined) &&
    formField?.fieldOptions?.enable === true
  )
    state?.setValue(formField.id, "");
  let recommedationData: any = [];
  let isFormSubmitted = useFormFieldStore.getState().isFormSubmitted;
  const isViewMode = query?.mode === FormMode.View;
  const isViewRecommendation = query?.mode === FormMode.ViewRecommendation;
  if (
    isViewMode &&
    formField?.recommendationCalc?.recommendation !== undefined &&
    isFormSubmitted
  ) {
    const StoreAnswer: any = useFormFieldStore.getState().answer;
    recommedationData = jsonata(
      formField?.recommendationCalc?.recommendation
    ).evaluate(StoreAnswer);
  }
  // console.log("render", "InputMultiline", formField.field);
  let EnableQuestionField = false;
  let getInterimRecomendation: any =
    useInterimAnswerStore.getState().interim_answers;
  let getSpecificRecomm = getInterimRecomendation[formField.field];
  if (
    userSession?.user?.role === AppRoles.Invitee ||
    userSession?.user?.role === AppRoles.Responder
  ) {
    let chkIsAnyOpen = false;
    if (getSpecificRecomm?.interim_recommendation?.length > 0) {
      let chkIsAnyOpenLength =
        getSpecificRecomm?.interim_recommendation?.filter(
          (x: any) =>
            x.status !== RecommendationStatus.Closed &&
            x.status !== RecommendationStatus.PendingForApproval
        );
      if (chkIsAnyOpenLength?.length > 0) {
        chkIsAnyOpen = true;
      }
    } else if (
      !!getSpecificRecomm?.interim_answer?.Interim_Recommendations &&
      getSpecificRecomm?.interim_answer?.Interim_Recommendations?.length > 0
    ) {
      let chkIsAnyOpenLength =
        getSpecificRecomm?.interim_answer?.Interim_Recommendations?.filter(
          (x: any) =>
            x.status !== RecommendationStatus.Closed &&
            x.status !== RecommendationStatus.PendingForApproval
        );
      if (chkIsAnyOpenLength?.length > 0) {
        chkIsAnyOpen = true;
      }
    }

    EnableQuestionField =
      !getSpecificRecomm?.isViewOnly &&
      query?.mode === FormMode.ViewRecommendation &&
      formField.id === getSpecificRecomm?.formFieldId &&
      chkIsAnyOpen
        ? true
        : false;
  }
  const showassigner =
    formField?.groupField?.indexOf("tabs") > -1 ? "Show" : "";
  //#region Carousel Data Binding
  const AISuggestionCarouseldata: AICArouselData[] =
    getAISuggestionCarouselData(
      !!useFormFieldStore?.getState()?.Suggestions
        ? useFormFieldStore
            ?.getState()
            ?.Suggestions.filter((items) => items.formFieldId == formField?.id)
        : []
    );
  //#endregion
  const onChangeEvent = (
    value: string,
    isFromAI: boolean,
    isFromPopup: boolean,
    formFieldId: string
  ) => {
    setIsClicked(!isFromAI);
    setErrorMessagedetails = true;
    const reg = /^([^<>{}^]*)$/;
    if (reg.test(value)) {
      setTextValue(value);
      checkInputData(value);
      if (isFromPopup) {
        state.setValue(formFieldId, value);
      } else {
        state.setValue(formField.id, value);
      }
    }
  };

  let orientation = "";
  let parentField = useFormFieldStore
    .getState()
    .formFields.filter((items) => items?.field == formField?.groupField);
  let groupField = formField?.groupField;
  while (
    !!parentField &&
    parentField.length > 0 &&
    orientation != "horizontal"
  ) {
    orientation =
      parentField.length > 0
        ? useFormFieldStore
            .getState()
            .formFields.filter((items) => items?.field == groupField)[0]
            .displayOptions.orientation
        : "";
    groupField = parentField[0].groupField;
    parentField = useFormFieldStore
      .getState()
      .formFields.filter((items) => items?.field == parentField[0].groupField);
  }
  // console.log("orientation", orientation, formField.field);
  return (
    <Stack style={EnableQuestionField === true ? { pointerEvents: "all" } : {}}>
      <DisplayLabel
        text={formField.fieldOptions.label}
        isHeading={!!formField.interfaceOptions.isHeading}
        headingSize={formField.interfaceOptions.headingSize}
        infoIconProps={formField.interfaceOptions?.infoIconProps}
        subtitle={formField.interfaceOptions.subtitle}
        showassigner={showassigner}
        formField={formField}
      />
      {!!formField.interfaceOptions?.showTitleDivider && (
        <Divider size={"md"} color="orange" />
      )}
      <Group position="apart">
        <Stack w="93%">
          <Tooltip
            disabled={!!formField.interfaceOptions?.isToolTip ? false : true}
            multiline
            label={!!formField.interfaceOptions?.isToolTip ? getTextValue : ""}
          >
            <Textarea
              pl={formField.interfaceOptions.subtitle ? 30 : 0}
              autoComplete="off"
              placeholder={state.interfaceOptions?.placeholder}
              error={validationErrorMessage(
                state.fieldOptions?.required,
                state.value,
                formField?.interfaceOptions?.isHyperLink
                  ? "hyperlink"
                  : "string",
                setErrorMessagedetails,
                formField.validationRules ?? ""
              )}
              style={
                isViewRecommendation === false && isViewMode === false
                  ? {
                      position: "relative",
                      zIndex: 9,
                      width: "100%",
                    }
                  : { width: "100%" }
              }
              disabled={state.fieldOptions?.readonly}
              readOnly={query?.mode === FormMode.View}
              withAsterisk={state.fieldOptions?.required}
              // maxRows={state.interfaceOptions?.maxRows ?? 5}
              classNames={{
                label: "labelStyle",
                error: "mantine-Textarea-error",
              }}
              autosize
              // autosize={state.interfaceOptions.autosize}
              cols={state.interfaceOptions.maxColumns}
              maxLength={state.interfaceOptions.maxLength}
              value={getTextValue}
              onChange={(e) => {
                onChangeEvent(e.target.value, false, false, formField?.id);
                //setValue(formField.id, e.target.value);
              }}
              onBlur={(e) =>
                onChangeEvent(e.target.value, false, false, formField?.id)
              }
              data-formfieldId={formField?.id}
            />
          </Tooltip>
          {AISuggestionCarouseldata?.length > 0 &&
          !state.fieldOptions?.readonly ? (
            <AISuggestionCarousel
              data={AISuggestionCarouseldata}
              onSelectSingleValueCard={(value, isFromPopup, formFieldId) => {
                onChangeEvent(value, true, isFromPopup, formFieldId);
              }}
              formFieldId={formField?.id}
              isclicked={
                !!useFormFieldStore.getState().answer[formField?.field]
                  ? blankCheck.includes(
                      useFormFieldStore.getState().answer[formField?.field]
                        .value
                    )
                    ? true
                    : isclicked
                  : isclicked
              }
              type="textArea"
              isFile={false}
              halfWidth={orientation === "horizontal"}
            />
          ) : (
            <></>
          )}
        </Stack>
        {FormHasRecommendation?.length === 0 &&
        recommedationData.length > 0 &&
        recommedationData.some((item: any) => item.value === state.value) ? (
          <CommonRecommendation
            recommText={
              recommedationData.filter(
                (item: any) => item.value === state.value
              )[0].comment
            }
          />
        ) : (
          ""
        )}
        <AddRecommendationButton
          formField={formField}
          answerOptionData={
            getSpecificRecomm?.interim_recommendation[0]?.answeroption
          }
        />
      </Group>
      {!!children?.length && (
        <Stack>
          {children.map((field) => (
            <FormFieldRender key={field.id} formField={field} />
          ))}
        </Stack>
      )}
    </Stack>
  );
};

const SimpleInputMultiline: FormFieldControl<"input-multiline"> = ({
  formField,
  name,
  onChange,
  rowIndex,
  ansId,
  isStateValuefromAI,
  isShowSparkIcon,
}) => {
  const { query } = useRouter();
  const userSession = useUserSession();
  const recommendationNewResponce: any = userSession?.GlobalMaster?.filter(
    (x: any) => x.type === "Recommendation_new"
  );
  // const { data: recommendationNewResponce } = useGetGlobalMasterByTypeQuery({
  //   variables: { type: "Recommendation_new" },
  // });
  let FormHasRecommendation: any = recommendationNewResponce[0]?.data.filter(
    (rec: any) => rec.FormId === formField?.formId
  );
  const state = useChildFieldState<"input-multiline">(
    String(name),
    formField,
    rowIndex,
    ansId
  );
  let [getTextValue, setTextValue] = useState(
    isStateValuefromAI ? state?.value : ""
  );
  ////this state is just for formality to rerender child component of table element of multipledropdown
  const [isStateSetFromSuggestion, setIsStateSetFromSuggestion] =
    useState(isStateValuefromAI);

  const checkInputData = (str: any) => {
    if (str === "") state?.setValue("");
  };
  if (!state?.fieldOptions.enable) return <></>;
  if (getTextValue === "") getTextValue = state?.value;
  const changeHandler = (value: any, isSparkIconClick?: boolean) => {
    // console.log({ value });
    setTextValue(value);
    checkInputData(value);
    setErrorMessagedetails = true;
    state?.setValue(value);
    !!onChange && onChange(value, isSparkIconClick);
  };

  if (!name) return <></>;
  let recommedationData: any = [];
  const isViewMode = query?.mode === FormMode.View;
  if (
    isViewMode &&
    formField?.recommendationCalc?.recommendation !== undefined
  ) {
    const StoreAnswer: any = useFormFieldStore.getState().answer;
    recommedationData = jsonata(
      formField?.recommendationCalc?.recommendation
    ).evaluate(StoreAnswer);
  }
  let EnableQuestionField = false;
  let getInterimRecomendation: any =
    useInterimAnswerStore.getState().interim_answers;
  let getSpecificRecomm = getInterimRecomendation[formField.field];
  if (
    userSession?.user?.role === AppRoles.Invitee ||
    userSession?.user?.role === AppRoles.Responder
  ) {
    let chkIsAnyOpen = false;
    if (getSpecificRecomm?.interim_recommendation?.length > 0) {
      let chkIsAnyOpenLength =
        getSpecificRecomm?.interim_recommendation?.filter(
          (x: any) =>
            x.status !== RecommendationStatus.Closed &&
            x.status !== RecommendationStatus.PendingForApproval
        );
      if (chkIsAnyOpenLength?.length > 0) {
        chkIsAnyOpen = true;
      }
    } else if (
      !!getSpecificRecomm?.interim_answer?.Interim_Recommendations &&
      getSpecificRecomm?.interim_answer?.Interim_Recommendations?.length > 0
    ) {
      let chkIsAnyOpenLength =
        getSpecificRecomm?.interim_answer?.Interim_Recommendations?.filter(
          (x: any) =>
            x.status !== RecommendationStatus.Closed &&
            x.status !== RecommendationStatus.PendingForApproval
        );
      if (chkIsAnyOpenLength?.length > 0) {
        chkIsAnyOpen = true;
      }
    }

    EnableQuestionField =
      !getSpecificRecomm?.isViewOnly &&
      query?.mode === FormMode.ViewRecommendation &&
      formField.id === getSpecificRecomm?.formFieldId &&
      getSpecificRecomm?.interim_recommendation?.length > 0 &&
      chkIsAnyOpen
        ? true
        : false;
  }
  const showassigner =
    formField?.groupField?.indexOf("tabs") > -1 ? "Show" : "";
  return (
    <Stack
      spacing="sm"
      style={EnableQuestionField === true ? { pointerEvents: "all" } : {}}
    >
      {formField.interfaceOptions.showLabel && (
        <DisplayLabel
          //  text={formField.fieldOptions.label}
          isHeading={!!formField.interfaceOptions.isHeading}
          headingSize={formField.interfaceOptions.headingSize}
          //  infoIconProps={formField.interfaceOptions?.infoIconProps}
          subtitle={formField.interfaceOptions.subtitle}
          showassigner={showassigner}
        />
      )}
      {!!formField.interfaceOptions?.showTitleDivider && (
        <Divider size={"md"} color="orange" />
      )}
      {/* {JSON.stringify(!!formField.interfaceOptions?.isToolTip)} */}
      <Tooltip
        disabled={
          !!formField.interfaceOptions?.isToolTip
            ? getTextValue === "" || getTextValue === null
              ? true
              : false
            : true
        }
        multiline
        label={!!formField.interfaceOptions?.isToolTip ? getTextValue : ""}
      >
        <Textarea
          // pl={formField.interfaceOptions.subtitle ? 30 : 0}
          autoComplete="off"
          placeholder={formField.interfaceOptions?.placeholder}
          error={validationErrorMessage(
            formField.fieldOptions?.required,
            state?.value,
            formField?.interfaceOptions?.isHyperLink ? "hyperlink" : "string",
            setErrorMessagedetails,
            formField.validationRules ?? ""
          )}
          disabled={formField.fieldOptions?.readonly}
          withAsterisk={formField.fieldOptions?.required}
          classNames={{
            label: "labelStyle",
            error: "mantine-Textarea-error",
            input: "tableTextAreaStyles",
          }}
          autosize
          minRows={1}
          maxRows={1000}
          // autosize={state.interfaceOptions.autosize}
          maxLength={formField.interfaceOptions.maxLength}
          // maxRows={formField.interfaceOptions?.maxRows ?? ""}
          cols={formField.interfaceOptions.maxColumns}
          value={isStateValuefromAI ? state?.value : getTextValue || ""}
          onChange={(e) => {
            const reg = /^([^<>{}^]*)$/;
            if (reg.test(e.target.value)) {
              changeHandler(e.target.value);
            }
          }}
          onBlur={(e) => changeHandler(e.target.value)}
          rightSection={
            isShowSparkIcon ? (
              <ActionIcon
                onClick={(e: any) =>
                  changeHandler(
                    isStateValuefromAI ? state?.value : getTextValue,
                    true
                  )
                }
                variant="transparent"
              >
                <SparkleSvgIcon />
              </ActionIcon>
            ) : (
              <></>
            )
          }
          styles={{
            rightSection: {
              svg: {
                fill: "none !important",
              },
            },
          }}
        />
      </Tooltip>
      {FormHasRecommendation?.length === 0 &&
      recommedationData.length > 0 &&
      recommedationData.some((item: any) => item.value === state.value) ? (
        <CommonRecommendation
          recommText={
            recommedationData.filter(
              (item: any) => item.value === state.value
            )[0].comment
          }
        />
      ) : (
        ""
      )}
    </Stack>
  );
};

const InputMultilineWrapper: FormFieldControl<"input-multiline"> = (props) => {
  if (props.isSimple) return <SimpleInputMultiline {...props} />;
  return <InputMultiline {...props} />;
};

export default memo(
  InputMultilineWrapper,
  (prev, next) => !!prev.formField === !!next.formField
);
