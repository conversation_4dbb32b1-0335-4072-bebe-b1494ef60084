query getConsultantAssessmentList(
  $where: FormInvitation_bool_exp!
  $platformId: uuid
  $companyId: uuid
  $invitationType: [String!]
  $sourceType: String!
) {
  AddressMapping {
    id
  }
  GlobalMaster(
    where: {
      _and: {
        Platform: { id: { _eq: $platformId } }
        type: { _in: $invitationType }
      }
    }
  ) {
    id
    platformId
    type
    data
    __typename
  }
  CompanyForm(where: { companyId: { _eq: $companyId } }) {
    formId
    Form {
      GroupForms {
        formId
        __typename
      }
      __typename
    }
    __typename
  }
  FormInvitation(where: $where, order_by: { updated_at: desc }) {
    id
    interimCheck
    completion
    ValidationWarningLogs_aggregate {
      aggregate {
        count
      }
    }
    ParentCompanyMapping {
      Id
      CompanyId
      ParentCompanyId
      companyByParentcompanyid {
        id
        name
        __typename
      }
      Company {
        id
        name
        metadata
        __typename
      }
    }
    Form {
      id
      name
      GroupForms {
        groupFormId
        formId
        Form {
          name
          __typename
        }
        __typename
      }
      __typename
    }

    durationTo
    durationFrom
    status
    created_at
    created_by
    <PERSON><PERSON><PERSON><PERSON> {
      UserRoles {
        roleName
      }
    }
    isDataCurationSkipped
    WebCurations(order_by: { created_at: desc }, limit: 1) {
      id
      status
      error
    }
    AIBulkDocumentProcessings {
      id
      requestStatus
    }
    Sources(where: { type: { _eq: $sourceType } }) {
      SourceFile {
        id
        status
      }
    }
    FormSubmissions(where: { isActive: { _eq: true } }) {
      id
      status
      FormResults(
        where: {
          _and: [
            { questionId: { _is_null: true } }
            { sectionId: { _is_null: true } }
          ]
        }
      ) {
        score
        __typename
      }
      __typename
    }
    InvitationConsultantMappings {
      id
      invitationId
      consultantUserId
      consultantCompanyId
      Company {
        id
        name
        __typename
      }
      User {
        id
        name
        email
        __typename
      }
      __typename
    }
    InvitationComments(order_by: { created_at: desc }, limit: 1) {
      content
      created_at
    }
    __typename
    AssesseeUserMappings {
      id
      formId
      InvitationId
      IsActive
    }
  }
}
